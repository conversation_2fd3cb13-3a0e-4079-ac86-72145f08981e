<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="sign.SignTemplateSidebar">
        <div t-attf-class="o_sign_sidebar {{ isSidebarHidden ? 'd-none' : ''}} w-100 border-end border-secondary">
            <div class="o_sign_sidebar_documents_header_title">
                <h6 class="m-0 mx-2">Documents</h6>
                <div class="d-flex o_sign_add_doc_btn" t-if="!this.props.hasSignRequests">
                    <t t-call="sign.SignViews.buttons"/>
                </div>
            </div>
            <div class="d-flex flex-column align-items-stretch">
                <div class="d-flex flex-column">
                    <t t-foreach="this.props.documents.filter((doc) => !doc.deleted).sort((a,b) => a.sequence - b.sequence)" t-as="document" t-key="document.id">
                        <div t-attf-class="o_sign_sidebar_document_name {{ document.id === this.props.selectedDocumentId ? 'o_sign_sidebar_document_name_selected' : ''}}"
                            t-on-click="() => this.onUpdateSelectedDocument(document.id)"
                        >
                            <input type="text" t-attf-class="mx-1 o_sign_document_name_input o_input bg-transparent form-control rounded-0 {{ document.id === this.state.editableDocumentId ? '' : 'd-none' }}"
                                t-att-data-document-id="document.id"
                                t-att-value="document.display_name"
                                t-att-disabled="this.props.hasSignRequests"
                                t-on-change="ev => this.onDocumentNameChanged(document.id, ev)"
                                t-on-blur="onDocumentNameBlur"
                                t-on-keyup="ev => this.onFieldNameInputKeyUp(ev)"
                            />
                            <div t-attf-class="mx-1 o_sign_sidebar_document_name_text {{ document.id === this.state.editableDocumentId ? 'd-none' : '' }}"
                                t-att-title="document.display_name"
                                t-on-click="() => document.id === this.props.selectedDocumentId ? null : this.onUpdateSelectedDocument(document.id)"
                            >
                                <span t-att-title="document.display_name" class="text-truncate">
                                    <t t-esc="document.display_name"/>
                                </span>
                            </div>
                            <i t-if="!props.hasSignRequests" class="fa fa-pencil mx-1" t-on-click="() => this.onDocumentNameTextClick(document.id)"/>
                            <div t-if="this.props.documents.filter((doc) => !doc.deleted).length > 1">
                                <Dropdown>
                                    <button class="btn o_sign_sidebar_icon">
                                        <i class="oi oi-ellipsis-v"/>
                                    </button>
                                    <t t-set-slot="content">
                                        <DropdownItem onSelected="() => this.onMoveDocumentUp(document.id)">
                                            <i class="fa fa-arrow-up fa-fw"/>
                                            Move Up
                                        </DropdownItem>
                                        <DropdownItem onSelected="() => this.onMoveDocumentDown(document.id)">
                                            <i class="fa fa-arrow-down fa-fw"/>
                                            Move Down
                                        </DropdownItem>
                                        <DropdownItem onSelected="() => this.onRemoveDocument(document.id)">
                                            <i class="fa fa-trash-o fa-fw text-danger"/>
                                            Delete
                                        </DropdownItem>
                                    </t>
                                </Dropdown>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
            <div class="o_sign_sidebar_signers_header_title mt-4">
                <h6 class="m-0 mx-2">Signers</h6>
                <button t-if="!props.hasSignRequests" class="btn btn-link fs-5 m-0" title="Create a new signer for signing the fields" t-on-click="onClickAddSigner">
                    Add
                </button>
            </div>
            <div>
                <t t-foreach="props.signers" t-as="signer" t-key="signer.id">
                    <SignTemplateSidebarRoleItems t-props="getSidebarRoleItemsProps(signer.id)"/>
                </t>
            </div>
            <div t-if="props.hasSignRequests" class="text-center p-3">
                <p class="text-info">Currently in <b>visualization</b> mode.</p>
                <button class="btn btn-secondary w-60" t-on-click="this.props.onEditTemplate">
                    <i class="fa fa-pencil mx-1"/> Edit Template
                </button>
            </div>
        </div>
    </t>
</templates>
