<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="sign.SignTemplateCustomCogMenu">
        <Dropdown>
            <button class="btn p-0 ms-1 border-0">
                <i class="fa fa-cog"/>
            </button>
            <t t-set-slot="content">
                <t t-if="showEditButton">
                    <DropdownItem class="'o_menu_item'" onSelected.bind="props.onEditTemplate">
                        <i class="fa fa-file-text fa-fw"/> Edit
                    </DropdownItem>
                </t>
                <t t-if="!props.signTemplate.active &amp;&amp; !state.properties">
                    <DropdownItem class="'o_menu_item'" onSelected.bind="props.onTemplateSaveClick">
                        <i class="fa fa-save fa-fw"/> Save as Template
                    </DropdownItem>
                </t>
                <t t-if="props.manageTemplateAccess">
                    <DropdownItem class="'o_menu_item'" onSelected.bind="onAccessRightsClick">
                        <i class="fa fa-group fa-fw"/> Access Rights
                    </DropdownItem>
                </t>
                <DropdownItem class="'o_menu_item'" onSelected.bind="onTemplatePropertiesClick">
                    <i class="fa fa-cog fa-fw"/> Configuration
                </DropdownItem>
                <DropdownItem class="'o_menu_item'" onSelected.bind="onPreviewClick">
                    <i class="fa fa-eye fa-fw"/> Preview
                </DropdownItem>
            </t>
        </Dropdown>
    </t>
</templates>
