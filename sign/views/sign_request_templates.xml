<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="sign._doc_sign" name="Document Sign">
        <div t-if="current_request_item and current_request_item.state == 'sent' and sign_request.state in ['sent', 'shared']" class="o_sign_next_document_banner bg-odoo">
            <button type="button" class="btn btn-primary o_next_document_button">
                Next Document
            </button>
        </div>
        <div t-if="current_request_item and current_request_item.state == 'sent' and sign_request.state in ['sent', 'shared']" class="o_sign_validate_banner bg-odoo">
            <button type="button" class="btn btn-primary o_validate_button">
                Validate <span class="o_sign_validate_text"> &amp; Send Completed Document</span>
            </button>
        </div>
        <div class="o_sign_cp d-none" t-if="not no_sign_cp">
            <div class="o_sign_cp_pager">
                <t t-call="sign.signer_status_wrapper"/>
            </div>
        </div>
        <t t-call="sign.documents_input_info"/>
        <t t-if="hasItems">
            <t t-call="sign.items_view"/>
        </t>

        <input id="o_sign_input_sign_request_id" type="hidden" t-att-value="sign_request.id"/>
        <input id="o_sign_input_sign_request_token" type="hidden" t-att-value="sign_request.access_token"/>
        <input id="o_sign_input_sign_request_state" type="hidden" t-att-value="sign_request.state"/>
        <input id="o_sign_input_sign_request_item_id" type="hidden" t-att-value="current_request_item.id"/>
        <input id="o_sign_input_access_token" type="hidden" t-att-value="token"/>
        <input id="o_sign_input_today_formatted_date" type="hidden" t-att-value="today_formatted_date"/>
        <input id="o_sign_input_date_format" type="hidden" t-att-value="date_format"/>
        <t t-if="sign_request.nb_closed == 0">
            <input id="o_sign_input_template_editable" type="hidden"/>
        </t>
        <input id="o_sign_input_auth_method" type="hidden" t-att-value="current_request_item.role_id.auth_method if current_request_item else False"/>
        <input id="o_sign_signer_name_input_info" type="hidden" t-att-value="current_request_item.partner_id.name if current_request_item and current_request_item.partner_id else None"/>
        <input id="o_sign_signer_phone_input_info" type="hidden" t-att-value="current_request_item.partner_id.phone if current_request_item and current_request_item.partner_id else None"/>
        <input id="o_sign_input_optional_redirect_url" type="hidden" t-att-value="sign_request.template_id.redirect_url"/>
        <input id="o_sign_input_optional_redirect_url_text" type="hidden" t-att-value="sign_request.template_id.redirect_url_text"/>
        <input t-if="current_request_item and current_request_item.state == 'sent'" id="o_sign_ask_location_input" type="hidden"/>
        <input id="o_sign_input_sign_frame_hash" type="hidden" t-att-value="frame_hash"/>
        <t t-if="not current_request_item.partner_id">
            <input id="o_sign_is_public_user" type="hidden"/>
        </t>
        <t t-if="show_thank_you_dialog">
            <input id="o_sign_show_thank_you_dialog" type="hidden"/>
        </t>
    </template>

    <template id="sign.doc_sign" name="Document Sign">
        <t t-call="web.layout">
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
                <t t-call-assets="sign.assets_public_sign" t-js="false"/>
                <script type="text/javascript">
                    odoo.__session_info__ = <t t-out="json.dumps(request.env['ir.http'].get_frontend_session_info_sign())"/>;
                </script>
                <t t-call-assets="sign.assets_public_sign" t-css="false"/>
                <t t-call="web.conditional_assets_tests">
                    <t t-set="ignore_missing_deps" t-value="True"/>
                </t>

                <script type="text/javascript">
                    odoo.define("sign.document_custom_page", ["@sign/components/sign_request/document_signable"], function (require) {
                        var { initDocumentToSign } = require("@sign/components/sign_request/document_signable");
                        initDocumentToSign(document);
                    });
                </script>
            </t>
            <t t-set="body_classname" t-value="'o_sign_document_body'"/>
            <div class="o_sign_document">
                <header>
                    <div class="o_sign_info justify-content-between d-flex">
                        <div class="o_logo_wrapper justify-content-start d-flex">
                            <div class="o_logo d-flex">
                                <a href="/"><img t-attf-src="/logo.png?company={{ company_id }}" alt="Logo"/></a>
                            </div>
                            <div class="o_odoo">
                                <a href="https://www.odoo.com/app/sign?utm_source=db&amp;utm_medium=sign"><img src="/sign/static/img/odoo_signed.png" alt="Signed"/></a>
                            </div>
                        </div>
                        <div class="justify-content-end d-flex">
                            <t t-call="sign.signer_status_wrapper">
                                <t t-set="portal" t-value="1"/>
                            </t>
                            <t t-set="has_actions" t-value="(current_request_item and current_request_item.state == 'sent') or sign_request.state == 'signed'"/>
                            <div class="dropdown d-flex btn-group m-1" t-if="portal or has_actions">
                                <!-- Web page dropdown: visible only when user is logged in with options to be shown. -->
                                <button t-if="portal" class="mobile-tablet-hide btn btn-secondary dropdown-toggle btn-sm" data-bs-toggle="dropdown" aria-label="Dropdown menu" title="Dropdown menu"/>
                                <div t-if="not portal and current_request_item and current_request_item.state == 'sent'" class="dropdown">
                                    <button
                                        t-if="current_request_item and current_request_item.state == 'sent'"
                                        class="btn btn-primary mobile-tablet-hide dropdown-toggle btn-sm"
                                        data-bs-toggle="dropdown"
                                        aria-expanded="false"
                                    />
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item o_sign_refuse_document_button">Decline to sign</a></li>
                                    </ul>
                                </div>
                                <div t-if="portal" class="mobile-tablet-hide dropdown-menu dropdown-menu-end" role="menu">
                                    <a t-if="sign_request.state == 'signed'" role="menuitem" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/completed" class="dropdown-item">Document <i class="fa fa-download"/></a>
                                    <a t-if="sign_request.state == 'signed'" role="menuitem" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/log" class="dropdown-item">Certificate <i class="fa fa-download"/></a>
                                    <div t-if="sign_request.state == 'signed' and portal" class="dropdown-divider"></div>
                                    <button t-if="current_request_item and current_request_item.state == 'sent'" class="btn btn-primary dropdown-item o_sign_refuse_document_button">Decline To Sign</button>
                                    <a t-if="portal" role="menuitem" t-attf-href="/my/" class="dropdown-item">Home</a>
                                    <a t-if="portal" role="menuitem" t-attf-href="/my/signature/{{current_request_item.id}}?{{ keep_query() }}" class="dropdown-item">Details</a>
                                </div>
                                <!-- Mobile dropdown: always visible with the 'Refuse' option. -->
                                <button class="mobile-tablet-show btn btn-secondary dropdown-toggle btn-sm" data-bs-toggle="dropdown" aria-label="Dropdown menu" title="Dropdown menu"/>
                                <div class="mobile-tablet-show dropdown-menu dropdown-menu-end" role="menu">
                                    <a t-if="sign_request.state == 'signed'" role="menuitem" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/completed" class="dropdown-item">Document <i class="fa fa-download"/></a>
                                    <a t-if="sign_request.state == 'signed'" role="menuitem" t-attf-href="/sign/download/{{sign_request.id}}/{{sign_request.access_token}}/log" class="dropdown-item">Certificate <i class="fa fa-download"/></a>
                                    <div t-if="sign_request.state == 'signed' and portal" class="dropdown-divider"></div>
                                    <button t-if="current_request_item and current_request_item.state == 'sent'" role="menuitem" class="btn dropdown-item o_sign_refuse_document_button mobile-tablet-show">Decline to sign</button>
                                    <a t-if="portal" role="menuitem" t-attf-href="/my/" class="dropdown-item">Home</a>
                                    <a t-if="portal" role="menuitem" t-attf-href="/my/signature/{{current_request_item.id}}?{{ keep_query() }}" class="dropdown-item">Details</a>
                                </div>
                            </div>
                            <div class="o_sign_dropdown_placeholder" t-if="not (portal or has_actions)"/>
                        </div>
                    </div>
                </header>
                <t t-set="no_sign_cp" t-value="True"/>
                <t t-call="sign._doc_sign"/>
            </div>
        </t>
    </template>

    <template id="sign.signer_status_wrapper" name="">
        <div class="o_sign_signer_status_wrapper justify-content-end d-flex">
            <div t-if="current_request_item" t-attf-class="o_sign_signer_status ps-4 {{'o_sign_signer_completed' if current_request_item.state == 'completed' else ''}}" t-att-data-id="current_request_item.id">
                <img t-if="current_request_item.state == 'completed' and current_request_item.signature" class="mobile-hide" t-attf-src="data:image/png;base64,{{current_request_item.signature}}" alt="Signature"/>
                <div class="o_sign_signer_status_info flex-column">
                    <div class="text-start"><b><t t-esc="current_request_item.partner_id.name if current_request_item.partner_id else 'Public user'"/></b></div>
                    <div class="text-start"><small><i>
                        <t t-if="current_request_item.state == 'sent'"> Is Signing </t>
                        <t t-if="current_request_item.state == 'canceled'"> Cancelled </t>
                        <t t-if="current_request_item.state == 'completed'"> Signed on <t t-esc="current_request_item.signing_date"/></t>
                    </i></small></div>
                </div>
            </div>
            <div class="o_sign_non_current_signers justify-content-end">  <!-- this div makes sure all non-current signers will be shown/hidden together when the space is/isn't enough -->
                <t t-foreach="state_to_sign_request_items_map.get('sent')" t-as="sign">
                    <div t-if="sign.access_token != token" class="o_sign_signer_status o_sign_signer_waiting ps-4" t-att-data-id="sign.id">
                        <div class="o_sign_signer_status_info flex-column">
                            <div class="text-start"><b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b></div>
                            <div class="text-start"><small><i> Waiting Signature </i></small></div>
                        </div>
                    </div>
                </t>
                <t t-foreach="state_to_sign_request_items_map.get('canceled')" t-as="sign">
                    <div t-if="sign.access_token != token" class="o_sign_signer_status o_sign_signer_waiting" t-att-data-id="sign.id">
                        <div class="o_sign_signer_status_info flex-column ps-4">
                            <div class="text-start"><b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b></div>
                            <div class="text-start"><small><i> Cancelled </i></small></div>
                        </div>
                    </div>
                </t>
                <t t-foreach="state_to_sign_request_items_map.get('completed')" t-as="sign">
                    <div t-if="sign.access_token != token" class="o_sign_signer_status o_sign_signer_completed d-flex">
                        <img t-if="sign.signature" class="mobile-hide" t-attf-src="data:image/png;base64,{{sign.signature}}" alt="Signature"/>
                        <div class="o_sign_signer_status_info flex-column ps-4">
                            <div class="text-start"><b><t t-esc="sign.partner_id.name if sign.partner_id else 'Public user'"/></b></div>
                            <div class="text-start"><small><i> Signed on <t t-esc="sign.signing_date"/></i></small></div>
                        </div>
                    </div>
                </t>
                <div t-attf-class="o_sign_document_navigator {{'d-none' if len(sign_request.template_id.document_ids) == 1 else ''}}">
                    <span class="o_sign_document_navigator_text"></span>
                    <div class="d-flex gap-1">
                        <t t-if="portal">
                            <button class="o_sign_document_navigator_left_arrow o_sign_portal_navigator rounded-start">
                                &lt;
                            </button>
                            <button class="o_sign_document_navigator_right_arrow o_sign_portal_navigator rounded-end">
                                &gt;
                            </button>
                        </t>
                        <t t-else="">
                            <button class="o_sign_document_navigator_left_arrow btn btn-secondary px-2 rounded-start">
                                <i class="oi oi-chevron-left"/>
                            </button>
                            <button class="o_sign_document_navigator_right_arrow btn btn-secondary px-2 rounded-end">
                                <i class="oi oi-chevron-right"></i>
                            </button>
                        </t>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="sign.deleted_sign_request" name="Signature Request Not Found">
        <t t-call="web.frontend_layout">
            <t t-set="head">
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            </t>
            <div class="oe_structure oe_empty">
                <section class="s_text_image pt104 pb104" data-snippet="s_image_text" data-name="Image - Text">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-4 pb32">
                                <img class="img img-fluid mx-auto" src="/web_editor/shape/http_routing/404.svg"/>
                            </div>
                            <div class="col-lg-8 text-lg-start text-center my-auto">
                                <h1 class="visually-hidden">Error 404</h1>
                                <h2>We couldn't find the signature request you're looking for!</h2>
                                <p/>
                                <p>The Odoo Sign document you are trying to reach does not exist. The signature request might have been deleted or modified.</p>
                                <p>You can contact the person who invited you to sign the document by email for help.</p>
                            </div>
                            <div class="col-lg-12">
                                <div class="s_hr pt32 pb48" data-snippet="s_hr" data-name="Separator">
                                    <hr class="w-100 mx-auto" style="border-top-width: 1px; border-top-style: solid; border-top-color: var(--400);"/>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <p class="text-center">Sign up for Odoo Sign to manage your own documents and signature requests!</p>
                                <ul class="list-inline text-center">
                                    <li class="list-inline-item mb-2"><a href="https://www.odoo.com/trial?selected_app=sign&amp;utm_source=db&amp;utm_medium=sign_404" target="blank" class="btn btn-secondary">Try Odoo Sign</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <template id="sign_request_expired" name="sign.sign_request_expired">
        <t t-call="web.frontend_layout">
            <div class="h-100 w-100 w-md-75 m-auto py-3 px-5 d-flex flex-column align-items-center justify-content-start text-center gap-3">
                <div class="card text-bg-light px-5 py-2 justify-content-center">
                    <h1>This link has expired.</h1>
                    <h6>
                        There's no reason to panic, <br/>
                        you can still sign your document in a few clicks!
                    </h6>
                </div>
                <p class="text-muted small"><i class="fa fa-info-circle"/> Links sent via email expire after a set delay to increase security.</p>
                <p>You can request a new link to access your document and sign it, it will  be delivered in your inbox right away.</p>
                <div class="d-flex flex-column align-items-center gap-1">
                    <a t-attf-class="btn btn-primary {{'disabled' if state == 'sent' else ''}}" t-att-href="resend_expired_link">Send a new link</a>
                    <small class="text-success" t-if="state == 'sent'"><i class="fa fa-check"/> A fresh link has just been sent to your inbox!</small>
                </div>
            </div>
        </t>
    </template>

</odoo>
