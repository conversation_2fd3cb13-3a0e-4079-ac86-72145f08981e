id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_hr_payroll_structure,hr.payroll.structure,model_hr_payroll_structure,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_structure_hr_user,hr.payroll.structure.hr.user,model_hr_payroll_structure,hr.group_hr_user,1,0,0,0
access_hr_payroll_structure_hr_contract_manager,hr.payroll.structure.hr.contract.manager,model_hr_payroll_structure,hr.group_hr_manager,1,0,0,0
access_hr_salary_rule_category,hr.salary.rule.category,model_hr_salary_rule_category,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip,hr.payslip,model_hr_payslip,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_line,hr.payslip.line,model_hr_payslip_line,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_input_user,hr.payslip.input.user,model_hr_payslip_input,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_input_type_user,hr.payslip.input.type.user,model_hr_payslip_input_type,hr_payroll.group_hr_payroll_user,1,0,0,0
access_hr_payslip_input_type_manager,hr.payslip.input.type.manager,model_hr_payslip_input_type,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payslip_worked_days_officer,hr.payslip.worked_days.officer,model_hr_payslip_worked_days,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_run,hr.payslip.run,model_hr_payslip_run,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_salary_rule_user,hr.salary.rule.user,model_hr_salary_rule,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_work_entry_type_manager,access_hr_work_entry_type_manager,model_hr_work_entry_type,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_manager,access_hr_rule_parameter_manager,model_hr_rule_parameter,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_user,access_hr_rule_parameter_user,model_hr_rule_parameter,hr.group_hr_user,1,0,0,0
access_hr_rule_parameter_value_manager,access_hr_rule_parameter_value_manager,model_hr_rule_parameter_value,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_value_user,access_hr_rule_parameter_value_user,model_hr_rule_parameter_value,hr.group_hr_user,1,0,0,0
access_hr_payroll_structure_type,hr.payroll.structure.type,model_hr_payroll_structure_type,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_index,access.hr.payroll.index,model_hr_payroll_index,hr.group_hr_manager,1,1,1,0
access_hr_work_entry_report_manager,hr.work.entry.report,model_hr_work_entry_report,hr_payroll.group_hr_payroll_user,1,0,0,0
access_hr_payroll_edit_payslip_lines_wizard,access.hr.payroll.edit.payslip.lines.wizard,model_hr_payroll_edit_payslip_lines_wizard,hr_payroll.group_hr_payroll_user,1,1,1,0
access_hr_payroll_edit_payslip_line,access.hr.payroll.edit.payslip.line,model_hr_payroll_edit_payslip_line,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_edit_payslip_worked_days_line,access.hr.payroll.edit.payslip.worked.days.line,model_hr_payroll_edit_payslip_worked_days_line,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_salary_assignment,hr.salary.attachment,model_hr_salary_attachment,hr_payroll.group_hr_payroll_user,1,1,1,1
hr_work_entry.access_hr_work_entry_system,access_hr_work_entry_system,hr_work_entry.model_hr_work_entry,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_note,hr.payroll.note,model_hr_payroll_note,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_employee_declaration,access_hr_payroll_employee_declaration,model_hr_payroll_employee_declaration,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_dashboard_warning_user,access_hr_payroll_dashboard_warning_user,model_hr_payroll_dashboard_warning,hr_payroll.group_hr_payroll_user,1,0,0,0
access_hr_payroll_dashboard_warning_manager,access_hr_payroll_dashboard_warning_manager,model_hr_payroll_dashboard_warning,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payroll_payment_report_wizard,hr.payroll.payment.report.wizard,model_hr_payroll_payment_report_wizard,hr_payroll.group_hr_payroll_user,1,1,1,0
access_hr_payroll_headcount,access_hr_payroll_headcount,model_hr_payroll_headcount,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_headcount_line,access_hr_payroll_headcount_line,model_hr_payroll_headcount_line,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_headcount_working_rate,access_hr_payroll_headcount_working_rate,model_hr_payroll_headcount_working_rate,hr_payroll.group_hr_payroll_user,1,0,1,1
access_hr_payroll_master_report,access_hr_payroll_master_report,model_hr_payroll_master_report,hr_payroll.group_hr_payroll_user,1,1,1,1
