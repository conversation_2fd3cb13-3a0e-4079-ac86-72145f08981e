<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="hr_payroll_dashboard_warning_employee_without_contract" model="hr.payroll.dashboard.warning">
            <field name="name">Employees Without Running Contracts</field>
            <field name="country_id" eval="False"/>
            <field name="color">1</field>
            <field name="evaluation_code">
# Retrieve employees:
# - with no open contract, and date_end in the past
# - with no contract, and not green draft contract
employees_without_contracts = self.env['hr.employee']
all_employees = self.env['hr.employee'].search([
    ('employee_type', '=', 'employee'),
    ('company_id', 'in', self.env.companies.ids),
])
today = date.today()
for employee in all_employees:
    version = employee.version_id.sudo()
    if not version.contract_date_start or (version.contract_date_end and version.contract_date_end &lt; today):
        employees_without_contracts += employee
if employees_without_contracts:
    warning_count = len(employees_without_contracts)
    warning_records = employees_without_contracts
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_employees_multiple_payslips" model="hr.payroll.dashboard.warning">
            <field name="name">Employees With Multiple Open Payslips of Same Type</field>
            <field name="country_id" eval="False"/>
            <field name="color">3</field>
            <field name="evaluation_code">
employee_payslips = defaultdict(lambda: defaultdict(lambda: self.env['hr.payslip']))
for slip in last_batches.slip_ids:
    if slip.state == 'cancel':
        continue
    employee = slip.employee_id
    struct = slip.struct_id

    employee_payslips[struct][employee] |= slip


employees_multiple_payslips = self.env['hr.payslip']
for dummy, employee_slips in employee_payslips.items():
    for employee, payslips in employee_slips.items():
        if len(payslips) &gt; 1:
            employees_multiple_payslips |= payslips
if employees_multiple_payslips:
    warning_count = len(employees_multiple_payslips.employee_id)
    warning_records = employees_multiple_payslips
    additional_context = {'search_default_group_by_employee_id': 1}
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_employee_missing_from_open_batch" model="hr.payroll.dashboard.warning">
            <field name="name">Employees (With Running Contracts) missing from open batches</field>
            <field name="country_id" eval="False"/>
            <field name="color">2</field>
            <field name="evaluation_code">
employees_missing_payslip = self.env['hr.employee'].search([
    ('company_id', 'in', last_batches.company_id.ids),
    ('id', 'not in', last_batches.slip_ids.employee_id.ids)]).filtered(lambda e: e.is_in_contract)
if employees_missing_payslip:
    warning_count = len(employees_missing_payslip)
    warning_records = employees_missing_payslip
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_work_entries_in_conflict" model="hr.payroll.dashboard.warning">
            <field name="name">Conflicts</field>
            <field name="country_id" eval="False"/>
            <field name="color">5</field>
            <field name="evaluation_code">
start_month = date.today().replace(day=1)
next_month = start_month + relativedelta(months=1)
work_entries_in_conflict = self.env['hr.work.entry'].search_count([
    ('state', '=', 'conflict'),
    ('date', '&gt;=', start_month),
    ('date', '&lt;', next_month)])
if work_entries_in_conflict:
    warning_count = work_entries_in_conflict
    warning_action = 'hr_work_entry.hr_work_entry_action_conflict'
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_nearly_expired_contracts" model="hr.payroll.dashboard.warning">
            <field name="name">Employees with running contracts coming to an end</field>
            <field name="country_id" eval="False"/>
            <field name="color">5</field>
            <field name="evaluation_code">
# Nearly expired contracts
nearly_expired_contracts = self.env['hr.version']
for company in self.env.companies:
    outdated_days = date.today() + relativedelta(days=company.contract_expiration_notice_period)
    nearly_expired_contracts += self.env['hr.version']._get_nearly_expired_contracts(outdated_days, company.id)
if nearly_expired_contracts:
    warning_count = len(nearly_expired_contracts.employee_id)
    warning_records = nearly_expired_contracts.employee_id
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_payslips_previous_contract" model="hr.payroll.dashboard.warning">
            <field name="name">Payslips Generated On Previous Contract</field>
            <field name="country_id" eval="False"/>
            <field name="color">6</field>
            <field name="evaluation_code">
employee_payslip_contracts = defaultdict(lambda: self.env['hr.version'])
for slip in last_batches.slip_ids:
    if slip.state == 'cancel':
        continue
    employee = slip.employee_id
    contract = slip.version_id
    employee_payslip_contracts[employee] |= contract

employees_previous_contract = self.env['hr.employee']
for employee, used_contracts in employee_payslip_contracts.items():
    if employee.version_id not in used_contracts:
        employees_previous_contract |= employee

if employees_previous_contract:
    warning_count = len(employees_previous_contract)
    warning_records = employees_previous_contract
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_payslips_negative_net" model="hr.payroll.dashboard.warning">
            <field name="name">Payslips With Negative NET</field>
            <field name="country_id" eval="False"/>
            <field name="color">4</field>
            <field name="evaluation_code">
payslips_with_negative_net = self.env['hr.payslip']

for slip in last_batches.slip_ids:
    if slip.state == 'cancel':
        continue
    if slip.net_wage &lt; 0:
        payslips_with_negative_net |= slip

if payslips_with_negative_net:
    warning_count = len(payslips_with_negative_net)
    warning_records = payslips_with_negative_net
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_employee_without_identification" model="hr.payroll.dashboard.warning">
            <field name="name">Employees Without Identification Number</field>
            <field name="country_id" eval="False"/>
            <field name="color">8</field>
            <field name="evaluation_code">
employees_wo_id = self.env['hr.employee'].search([
    ('identification_id', '=', False),
    ('company_id', 'in', self.env.context.get('allowed_company_ids')),
])
if employees_wo_id:
    warning_count = len(employees_wo_id)
    warning_records = employees_wo_id
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_employee_without_bank_account" model="hr.payroll.dashboard.warning">
            <field name="name">Employees Without Bank account Number</field>
            <field name="country_id" eval="False"/>
            <field name="color">9</field>
            <field name="evaluation_code">
employees_wo_bnk_acc = self.env['hr.employee'].search([
    ('bank_account_id', '=', False),
    ('company_id', 'in', self.env.context.get('allowed_company_ids')),
])
if employees_wo_bnk_acc:
    warning_count = len(employees_wo_bnk_acc)
    warning_records = employees_wo_bnk_acc
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_untrusted_bank_accounts" model="hr.payroll.dashboard.warning">
            <field name="name">Employees with untrusted Bank Account numbers</field>
            <field name="country_id" eval="False"/>
            <field name="color">10</field>
            <field name="evaluation_code">
employee_bank_data = self.env['hr.employee']._get_account_holder_employees_data()
untrusted_bank_employee_ids = self.env['hr.employee']._get_untrusted_bank_employee_ids(employee_bank_data)
untrusted_bank_accounts = (self.env['hr.employee'].browse(untrusted_bank_employee_ids))['bank_account_id']
if untrusted_bank_accounts:
    warning_count = len(untrusted_bank_accounts)
    warning_records = untrusted_bank_accounts
            </field>
        </record>

        <record id="hr_payroll_dashboard_warning_payslip_overdue" model="hr.payroll.dashboard.warning">
            <field name="name">Payslips are overdue for payments</field>
            <field name="country_id" eval="False"/>
            <field name="evaluation_code">
today = date.today()
done_payslips = self.env['hr.payslip'].search([
    ('state', '=', 'done'),
    ('company_id', 'in', self.env.companies.ids),
])
overdue_payslips = self.env['hr.payslip']
for payslip in done_payslips:
    if (today - payslip.create_date.date()).days &gt;= 30:
        overdue_payslips += payslip
if overdue_payslips:
    warning_count = len(overdue_payslips)
    warning_records = overdue_payslips
            </field>
        </record>
    </data>
</odoo>
