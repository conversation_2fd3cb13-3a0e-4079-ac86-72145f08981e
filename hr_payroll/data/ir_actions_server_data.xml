<?xml version='1.0' encoding='UTF-8' ?>
<odoo>
    <record model="ir.actions.server" id="action_reset_work_entries">
        <field name="name">Payroll - Technical: Reset Work Entries</field>
        <field name="model_id" ref="hr_work_entry.model_hr_work_entry"/>
        <field name="state">code</field>
        <field name="group_ids" eval="[(4,ref('base.group_system'))]"/>
        <field name="code">
# Don't call this server action if you don't want to loose all your work entries
env['hr.work.entry'].search([]).unlink()
now = datetime.datetime.now()
env['hr.version'].write({
    'date_generated_from': now,
    'date_generated_to': now
})
        </field>
    </record>

    <record id="action_edit_payslip_lines" model="ir.actions.server">
        <field name="name">Edit Payslip Lines</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">
action = records.action_edit_payslip_lines()
        </field>
    </record>
</odoo>
