<?xml version='1.0' encoding='UTF-8' ?>
<odoo>
    <record id="hr_work_entry_enterprise.menu_hr_payroll_root" model="ir.ui.menu">
        <field name="group_ids" eval="[Command.link(ref('hr_payroll.group_hr_payroll_user'))]"/>
    </record>

    <menuitem
        id="menu_hr_payroll_dashboard_root"
        name="Dashboard"
        parent="hr_work_entry_enterprise.menu_hr_payroll_root"
        action="hr_payroll_dashboard_open"
        sequence="40"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_employees"
        name="Employees"
        parent="hr_work_entry_enterprise.menu_hr_payroll_root"
        sequence="50"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_work_entries_root"
        name="Work Entries"
        parent="hr_work_entry_enterprise.menu_hr_payroll_root"
        sequence="60"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_payslips"
        name="Payslips"
        parent="hr_work_entry_enterprise.menu_hr_payroll_root"
        sequence="70"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_report"
        name="Reporting"
        parent="hr_work_entry_enterprise.menu_hr_payroll_root"
        sequence="70"
        groups="hr_payroll.group_hr_payroll_user"/>

    <!-- **** Employees **** -->
    <menuitem
        id="hr_menu_employees"
        name="Employees"
        action="hr.open_view_employee_list_my"
        parent="menu_hr_payroll_employees"
        groups="hr_payroll.group_hr_payroll_user"
        sequence="10"/>

    <menuitem
        id="hr_menu_versions"
        name="Versions"
        action="hr.action_hr_version"
        parent="menu_hr_payroll_employees"
        sequence="20"
        groups="base.group_no_one"/>

    <menuitem
        id="hr_menu_salary_attachments"
        name="Salary Attachments"
        action="hr_payroll.hr_salary_attachment_action"
        parent="menu_hr_payroll_employees"
        groups="hr_payroll.group_hr_payroll_user"
        sequence="30"/>

    <!-- **** Payslips **** -->
    <menuitem
        id="menu_hr_payroll_employee_payslips"
        name="Payslips"
        parent="menu_hr_payroll_payslips"
        sequence="60"
        action="action_view_hr_payslip_month_form"
        groups="hr_payroll.group_hr_payroll_user"/>

      <menuitem
        id="menu_hr_payslip_run"
        action="action_hr_payslip_run"
        name="Pay Runs"
        sequence="80"
        parent="menu_hr_payroll_payslips"/>

    <!-- **** Reporting **** -->
    <menuitem
        id="menu_hr_work_entry_report"
        name="Work Entries Analysis"
        action="hr_work_entry_report_action"
        sequence="2"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payroll_headcount_action"
        name="Headcount"
        action="hr_payroll_headcount_action"
        sequence="11"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payslip_line_action_report"
        name="Payslip Lines"
        action="hr_payslip_line_action_report"
        sequence="12"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payslip_work_days_action_report"
        name="Payslip Work Days Lines"
        action="hr_payslip_work_days_action_report"
        sequence="13"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payslip_input_action_report"
        name="Payslip Other Inputs"
        action="hr_payslip_input_action_report"
        sequence="14"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payroll_master_report"
        name="Master Report"
        action="action_hr_payroll_master_report"
        sequence="20"
        parent="menu_hr_payroll_report"/>

    <!-- **** Configuration **** -->
    <record id="hr_work_entry_enterprise.menu_hr_payroll_configuration" model="ir.ui.menu">
        <field name="group_ids" eval="[Command.set([ref('hr_payroll.group_hr_payroll_manager')])]"/>
    </record>

    <menuitem
        id="menu_hr_payroll_global_settings"
        name="Settings"
        parent="hr_work_entry_enterprise.menu_hr_payroll_configuration"
        sequence="0"
        action="action_hr_payroll_configuration"
        groups="base.group_system"/>

    <!-- Salary Configuration -->
    <menuitem
        id="menu_hr_salary_configuration"
        name="Salary"
        parent="hr_work_entry_enterprise.menu_hr_payroll_configuration"
        sequence="80"/>

    <menuitem
        id="menu_action_hr_salary_rule_form"
        action="action_salary_rule_form"
        name="Rules"
        parent="menu_hr_salary_configuration"
        sequence="30"/>

    <menuitem
        id="menu_action_hr_salary_rule_parameter"
        action="hr_rule_parameter_action"
        name="Rule Parameters"
        parent="menu_hr_salary_configuration"
        sequence="35"/>

    <menuitem
        id="menu_hr_salary_rule_category"
        action="action_hr_salary_rule_category"
        name="Rule Categories"
        parent="menu_hr_salary_configuration"
        sequence="35"/>

    <menuitem
        id="menu_hr_payroll_structure_view"
        action="action_view_hr_payroll_structure_list_form"
        name="Structures"
        parent="menu_hr_salary_configuration"
        sequence="20"/>

    <menuitem
        id="menu_hr_payroll_structure_type"
        name="Structure Types"
        action="action_hr_payroll_structure_type"
        parent="menu_hr_salary_configuration"
        sequence="10"/>

    <menuitem
        id="menu_hr_payslip_entry_type_view"
        action="action_view_hr_payslip_input_type"
        parent="menu_hr_salary_configuration"
        name="Other Input Types"
        sequence="40"/>

    <!-- Dashboard Configuration -->
    <menuitem
        id="menu_hr_payroll_dashboard_configuration"
        name="Dashboard"
        parent="hr_work_entry_enterprise.menu_hr_payroll_configuration"
        sequence="100"/>

    <menuitem
        id="menu_hr_payroll_dashboard_warning"
        action="action_hr_payroll_dashboard_warning"
        parent="menu_hr_payroll_dashboard_configuration"
        name="Warnings"
        sequence="10"/>

</odoo>
