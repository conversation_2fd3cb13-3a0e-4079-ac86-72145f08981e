<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_salary_rule_list" model="ir.ui.view">
        <field name="name">hr.salary.rule.list</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <list string="Salary Rules">
                <field name="name"/>
                <field name="code"/>
                <field name="category_id"/>
                <field name="struct_id" column_invisible="True"/>
                <field name="sequence" column_invisible="True"/>
                <field name="partner_id"/>
                <field name="country_id" optional="hidden"/>
            </list>
        </field>
    </record>

    <record id="hr_salary_rule_view_list_m2m" model="ir.ui.view">
        <field name="name">hr.salary.rule.view.list.m2m</field>
        <field name="model">hr.salary.rule</field>
        <field name="inherit_id" ref="hr_payroll.hr_salary_rule_list"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="struct_id" position="attributes">
                <attribute name="column_invisible">0</attribute>
            </field>
            <field name="partner_id" position="attributes">
                <attribute name="optional">hidden</attribute>
            </field>
        </field>
    </record>

    <record id="hr_salary_rule_view_kanban" model="ir.ui.view">
        <field name="name">hr.salary.rule.kanban</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="card" class="row g-0">
                        <field name="name" class="col-8 fw-bolder"/>
                        <div class="col-4">
                            <field name="category_id" class="float-end text-end"/>
                        </div>
                        <span>Code: <field name="code"/></span>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_salary_rule_form" model="ir.ui.view">
        <field name="name">hr.salary.rule.form</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <form string="Salary Rules" js_class="hr_salary_rule_preview">
            <sheet>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                <label for="name" string="Rule Name"/>
                <h1><field name="name" placeholder="e.g. Net Salary"/></h1>

                <group name="main_details" col="4">
                   <field name="code"/>
                   <field name="category_id" placeholder="e.g. Net"/>
                   <field name="sequence"/>
                   <field name="struct_id"/>
                   <field name="active" widget="boolean_toggle"/>
                   <field name="appears_on_payslip"/>
                   <field name="appears_on_employee_cost_dashboard"/>
                </group>
                <notebook colspan="6">
                    <page name="general" string="General">
                        <group name="general_conditions">
                            <group string="Conditions">
                                <field name="condition_select"/>
                                <field name="condition_range" invisible="condition_select != 'range'" required="condition_select == 'range'"/>
                                <field name="condition_range_min" invisible="condition_select != 'range'" required="condition_select == 'range'"/>
                                <field name="condition_range_max" invisible="condition_select != 'range'" required="condition_select == 'range'"/>
                                <field name="condition_other_input_id" invisible="condition_select != 'input'" required="condition_select == 'input'"/>
                            </group>
                            <group colspan="4">
                                <field name="condition_python" invisible="condition_select != 'python'" required="condition_select == 'python'"/>
                            </group>
                            <group string="Computation">
                                <field name="amount_select"/>
                                <field name="amount_percentage_base" invisible="amount_select != 'percentage'" required="amount_select == 'percentage'"/>
                                <field name="quantity" invisible="amount_select in ['code', 'input']" required="amount_select not in ['code', 'input']"/>
                                <field name="amount_fix"  invisible="amount_select != 'fix'"  required="amount_select == 'fix'"/>
                                <field name="amount_other_input_id"  invisible="amount_select != 'input'"  required="amount_select == 'input'"/>
                                <field name="amount_percentage" invisible="amount_select != 'percentage'" required="amount_select == 'percentage'"/>
                            </group>
                            <group colspan="4">
                                <field name="amount_python_compute" invisible="amount_select != 'code'" required="amount_select == 'code'"/>
                            </group>
                            <group string="Company Contribution">
                                <field name="partner_id"/>
                            </group>
                        </group>
                    </page>
                    <page name="display" string="Display">
                        <group name="options" string="Options">
                            <group>
                                <field name="color" widget="color"/>
                                <field name="title"/>
                            </group>
                            <group>
                                <field name="bold"/>
                                <field name="underline"/>
                                <field name="italic"/>
                            </group>
                            <label for="note" string="Description" class="fw-bold text-900 opacity-100"/>
                            <field name="note"/>
                        </group>
                        <group name="preview" string="Preview">
                            <group>
                                <div>
                                    <field name="name" readonly="1" class="o_preview fs-4"/>
                                    <field name="note" readonly="1" class="text-truncate o_preview fs-6" invisible="not note"/>
                                </div>
                            </group>
                            <group>
                                <field name="preview_currency_position" invisible="1"/>
                                <field name="preview_currency_symbol" class="o_preview fs-4" nolabel="1" invisible="title or preview_currency_position == 'after'"/>
                                <span class="o_preview fs-4" invisible="title">99.99</span>
                                <field name="preview_currency_symbol" class="o_preview fs-4" nolabel="1" invisible="title or preview_currency_position == 'before'"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </sheet>
            </form>
        </field>
    </record>

    <record id="view_hr_rule_filter" model="ir.ui.view">
        <field name="name">hr.salary.rule.select</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <search string="Search Salary Rule">
                <field name="name" string="Salary Rules" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="category_id"/>
                <field name="struct_id"/>
                <field name="condition_range_min"/>
                <field name="amount_python_compute" string="Python Code" filter_domain="['|', ('amount_python_compute', 'ilike', self), ('condition_python', 'ilike', self)]"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                <group col="8" colspan="4">
                    <filter string="Category" name="head" context="{'group_by': 'category_id'}"/>
                    <filter string="Salary Structure" name="group_by_struct_id" context="{'group_by': 'struct_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_salary_rule_form" model="ir.actions.act_window">
        <field name="name">Salary Rules</field>
        <field name="res_model">hr.salary.rule</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="domain">[]</field>
        <field name="context">{'search_default_group_by_struct_id': 1}</field>
        <field name="search_view_id" ref="view_hr_rule_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new salary rule
            </p>
        </field>
    </record>
</odoo>
