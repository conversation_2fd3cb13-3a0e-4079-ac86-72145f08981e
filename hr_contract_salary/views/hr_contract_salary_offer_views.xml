<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_hr_offer_new" model="ir.actions.act_window">
        <field name="name">Offer</field>
        <field name="res_model">hr.contract.salary.offer</field>
        <field name="context">{'is_hr_payroll': True}</field>
        <field name="view_mode">form</field>
    </record>
    <record id="hr_contract_salary_offer_action" model="ir.actions.act_window">
        <field name="name">Offers</field>
        <field name="res_model">hr.contract.salary.offer</field>
        <field name="view_mode">list,form</field>
        <field name="context">{
            'search_default_group_by_state': True,
            'search_default_employees': True,
            'is_hr_payroll': True
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Manage your Offers
            </p><p>
                Offers are sent to employee with a predefined selection of data.
                It allows to reflect on the propositon and discuss it.<br/><br/>
                <a name="hr_contract_salary.action_hr_offer_new" type="action" class="btn btn-secondary">Create a Offer</a>
            </p>
        </field>
    </record>

    <record id="hr_contract_salary_offer_view_tree" model="ir.ui.view">
        <field name="name">hr.contract.salary.offer.view.list</field>
        <field name="model">hr.contract.salary.offer</field>
        <field name="arch" type="xml">
            <list string="Salary Package Offers" default_order="id desc">
                <field name="contract_start_date" optional="show" string="Date"/>
                <field name="display_name" optional="show"/>
                <field name="employee_id" invisible="context.get('default_employee_id') or not employee_version_id" optional="hide"/>
                <field name="applicant_id" invisible="context.get('default_applicant_id') or not applicant_id" optional="hide" context="{'show_partner_name': True}"/>
                <field name="final_yearly_costs" widget="monetary" options="{'currency_field': 'currency_id'}" optional="show"/>
                <field name="employee_job_id" optional="show"/>
                <field name="department_id" optional="hide"/>
                <field name="offer_end_date" optional="hide"/>
                <field name="contract_template_id" optional="hide"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                <field name="create_date" optional="hide"/>
                <field name="create_uid" optional="hide"/>
                <field name="state" widget="badge"
                decoration-muted="state == 'open'"
                decoration-info="state == 'half_signed'"
                decoration-success="state =='full_signed'"
                decoration-danger="state in ('expired', 'refused', 'cancelled')"
                optional="show"/>
                <field name="currency_id" column_invisible="True"/>
                <field name="employee_version_id" column_invisible="True"/>
            </list>
        </field>
    </record>

    <record id="hr_contract_salary_offer_view_form" model="ir.ui.view">
        <field name="name">hr.contract.salary.offer.view.form</field>
        <field name="model">hr.contract.salary.offer</field>
        <field name="arch" type="xml">
            <form string="Salary Package Offer">
                <header>
                    <button
                        name="action_send_by_email"
                        type="object"
                        data-hotkey="shift+e"
                        string="Send By Email"
                        class="oe_highlight"
                        invisible="not id or state in ['refused', 'expired', 'cancelled']"/>
                    <button
                        name="action_jump_to_offer"
                        type="object"
                        data-hotkey="shift+g"
                        string="Salary Configurator"
                        class="btn btn-secondary"
                        invisible="not id or state in ['refused', 'expired', 'cancelled']"/>
                    <field name="url" class="btn btn-addr ps-0" string="Copy link" widget="CopyClipboardButton"
                        options="{'btn_class': 'btn btn-secondary'}" invisible="not id"/>
                    <field name="state" widget="statusbar" invisible="not is_half_sign_state_required"
                        statusbar_visible="open,half_signed,full_signed" readonly="1"/>
                    <field name="state" widget="statusbar" invisible="is_half_sign_state_required"
                        statusbar_visible="open,full_signed" readonly="1"/>
                </header>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button
                            name="action_view_signature_request"
                            type="object"
                            data-hotkey="shift+g"
                            class="oe_stat_button"
                            icon="fa-pencil"
                            invisible="state != 'half_signed'">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Requested Signature</span>
                            </div>
                        </button>

                        <button
                            name="action_view_contract"
                            type="object"
                            data-hotkey="shift+g"
                            class="oe_stat_button"
                            icon="fa-book"
                            invisible="state != 'full_signed'">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Signed Contract</span>
                                </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Refused" bg_color="text-bg-danger" invisible="state != 'refused'"/>
                    <widget name="web_ribbon" title="Cancelled" bg_color="text-bg-danger" invisible="state != 'cancelled'"/>
                    <div class="alert alert-info text-center" role="status" invisible="state != 'full_signed'">
                        This offer has been fully signed and can no longer be edited. However, you can still make changes to the Signed Contract if needed.
                    </div>
                    <div class="oe_title pe-0">
                        <label for="display_name"/>
                        <h1>
                            <field name="display_name" options="{'line_breaks': False}" readonly="state == 'cancelled'" placeholder="e.g. Offer 70k Employer Cost"/>
                        </h1>
                    </div>
                    <group>
                        <group name="group_offer">
                            <field name="contract_template_id" readonly="state in ['cancelled', 'full_signed']"
                                context="{'form_view_ref': 'hr.hr_contract_template_form_view'}"
                                placeholder="Select a contract template to load its predefined values"/>
                            <label for="sign_template_id" string="PDF Template"/>
                            <div class="o_row">
                                <field name="sign_template_id" options="{'no_create': True}"
                                    readonly="contract_template_id" placeholder="Select the PDF that the employee will sign"/>
                                <button name="action_edit_offer_signatories" type="object" class="btn btn-secondary"
                                    string="Edit Signatories" invisible="sign_template_id" disabled="1"/>
                                <button name="action_edit_offer_signatories" type="object" class="btn btn-secondary"
                                    string="Edit Signatories" invisible="not sign_template_id"/>
                            </div>
                            <field name="final_yearly_costs" readonly="state in ['cancelled', 'full_signed']"/>
                            <field name="job_title" readonly="state == 'cancelled'"
                                placeholder="e.g. Lead Dev, Junior Consultant"/>
                            <field name="employee_job_id" readonly="state in ['cancelled', 'full_signed']"
                                placeholder="e.g. Developer, Sales, Consultant"/>
                            <field name="department_id" readonly="state == 'cancelled'"
                                placeholder="e.g. Human Resources, Research &amp; Development, Marketing"/>
                            <label for="contract_start_date" string="Contract Date"/>
                            <div class="o_row">
                                <field name="contract_start_date" readonly="state in ['cancelled', 'full_signed']"/>
                                <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                                <field name="contract_end_date" readonly="state in ['cancelled', 'full_signed']" placeholder="No limit"/>
                            </div>
                            <field name="offer_create_date" readonly="1"/>
                            <field name="offer_end_date" readonly="state in ['cancelled', 'full_signed']" placeholder="Always valid"/>
                        </group>
                        <group name="group_offer_to">
                            <field name="employee_id" invisible="applicant_id or not context.get('is_hr_payroll', True)"
                                readonly="not context.get('is_hr_payroll', False) or state == 'full_signed'" placeholder="Select an employee..."/>
                            <field name="applicant_id" invisible="employee_version_id or context.get('is_hr_payroll', False)"
                                readonly="context.get('is_hr_payroll', True) or state == 'full_signed'"
                                context="{'show_partner_name': True}" placeholder="Select an applicant..."/>
                            <field name="company_id" groups="base.group_multi_company" readonly="state in ['cancelled', 'full_signed']"/>
                            <field name="company_id" invisible="1" groups="!base.group_multi_company"/>
                            <field name="refusal_reason" readonly="1" invisible="not refusal_reason"/>
                            <field name="refusal_date" readonly="1" invisible="not refusal_date"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                        <group string="Token" name="group_token" groups="base.group_no_one">
                            <field name="access_token" readonly="state in ['cancelled', 'full_signed']"/>
                            <field name="create_date"/>
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="hr_contract_salary_offer_view_search" model="ir.ui.view">
        <field name="name">hr.contract.salary.offer.view.search</field>
        <field name="model">hr.contract.salary.offer</field>
        <field name="arch" type="xml">
            <search string="Search Offers">
                <field string="Applicant" name="applicant_name"/>
                <field name="employee_id"/>
                <field name="display_name"/>
                <filter name="employees" domain="[('employee_id', '!=', False)]" string="Employees"/>
                <filter name="applicants" domain="[('applicant_id', '!=', False)]" string="Applicants"/>
                <separator/>
                <filter name="expired" domain="[('offer_end_date', '&lt;', 'today')]" string="Expired"/>
                <separator/>
                <filter name="refused" domain="[('state', '=', 'refused')]" string="Refused"/>
                <group>
                    <filter string="State" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Job Position" name="group_by_employee_job_id" domain="[]" context="{'group_by': 'employee_job_id'}"/>
                    <filter string="Department" name="group_by_department_id" domain="[]" context="{'group_by': 'department_id'}"/>
                    <filter string="Contract Template" name="group_by_contract_template_id" domain="[]" context="{'group_by': 'contract_template_id'}"/>
                    <filter string="Employee" name="group_by_employee_id" domain="[]" context="{'group_by': 'employee_id'}"/>
                    <filter string="Applicant" name="group_by_applicant_id" domain="[]" context="{'group_by': 'applicant_id'}"/>
                    <filter string="Company" name="group_by_company_id" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record model="ir.actions.server" id="action_refuse_salary_offer">
            <field name="name">Refuse</field>
            <field name="model_id" ref="model_hr_contract_salary_offer"/>
            <field name="binding_model_id" ref="model_hr_contract_salary_offer" />
            <field name="state">code</field>
            <field name="code">
                action = records.action_open_refuse_wizard()
            </field>
    </record>

     <record id="hr_contract_salary_offer_recruitment_action" model="ir.actions.act_window">
        <field name="name">Offers</field>
        <field name="res_model">hr.contract.salary.offer</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('employee_id', '=', False)]</field>
        <field name="context">{'search_default_group_by_state': True, 'is_hr_payroll': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create new offer
            </p><p>
                Did you know that you can create an offer for any applicant?<br/>
                Why don't you try? They're listed <a type="action" class="text-primary" name="hr_recruitment.crm_case_categ0_act_job">here</a>
            </p>
        </field>
    </record>

    <menuitem
        id="menu_salary_package_offer"
        action="hr_contract_salary_offer_action"
        parent="hr_contract_salary.salary_package_menu"
        sequence="10"/>

    <menuitem id="menu_hr_contract_salary_job_offer"
          parent="hr_recruitment.menu_crm_case_categ0_act_job"
          action="hr_contract_salary_offer_recruitment_action"
          sequence="3"/>
</odoo>
