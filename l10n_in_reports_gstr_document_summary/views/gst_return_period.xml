<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <record id="l10n_in_gst_return_period_form_view_document_summary" model="ir.ui.view">
        <field name="name">l10n_in.gst.return.period.form.document.summary</field>
        <field name="model">l10n_in.gst.return.period</field>
        <field name="inherit_id" ref="l10n_in_reports.l10n_in_gst_return_period_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//ol[@id='gstr1-list']/li[1]" position="after">
                <li>
                    <span invisible="document_summary_line_ids or gstr1_status == 'filed'">
                        <button name="action_generate_document_summary" class="btn btn-link p-0" type="object" string="Generate Document Summary"/>
                    </span>
                    <span invisible="not document_summary_line_ids or gstr1_status == 'filed'">
                        <button name="action_generate_document_summary" class="btn btn-link p-0" type="object" string="Regenerate Document Summary"/>
                    </span>
                    <span invisible="gstr1_status != 'filed'">
                        Generate Document summary
                    </span>
                    and
                    <button name="action_open_document_summary" class="btn btn-link p-0" type="object" string="View"/>
                </li>
            </xpath>
        </field>
    </record>
</odoo>
