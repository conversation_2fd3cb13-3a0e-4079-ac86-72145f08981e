<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_basic_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.BASIC"/>
        <field name="name">Basic Salary</field>
        <field name="sequence">1</field>
        <field name="code">BASIC</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = payslip.paid_amount
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <!-- ALLOWANCES -->
    <record id="l10n_ke_employees_salary_untaxed_food_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_UT_ALW"/>
        <field name="name">Food Allowance</field>
        <field name="code">UNTAXED_FOOD_ALLOWANCE</field>
        <field name="sequence">11</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = 0 &lt; version.l10n_ke_food_allowance &lt;= payslip._rule_parameter('l10n_ke_food_alw_max')
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_food_allowance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_untaxed_airtime_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_UT_ALW"/>
        <field name="name">Airtime Allowance</field>
        <field name="code">UNTAXED_AIRTIME_ALLOWANCE</field>
        <field name="sequence">13</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = 0 &lt; version.l10n_ke_airtime_allowance &lt;= payslip._rule_parameter('l10n_ke_airtime_alw_max')
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_airtime_allowance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_untaxed_pension_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_UT_ALW"/>
        <field name="name">Pension Allowance</field>
        <field name="code">UNTAXED_PENSION_ALLOWANCE</field>
        <field name="sequence">14</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = bool(version.l10n_ke_pension_allowance)
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
maximum = payslip._rule_parameter('l10n_ke_pension_alw_max')
result = min(maximum, version.l10n_ke_pension_allowance)
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_untaxed_allowance_total" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Untaxed Allowance</field>
        <field name="code">UNTAXED_ALLOWANCE</field>
        <field name="sequence">20</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = categories['KE_UT_ALW']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">result = categories['KE_UT_ALW']</field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_taxed_food_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_T_ALW"/>
        <field name="name">Food Allowance</field>
        <field name="code">TAXED_FOOD_ALLOWANCE</field>
        <field name="sequence">21</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_food_allowance &gt; payslip._rule_parameter('l10n_ke_food_alw_max')
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_food_allowance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_taxed_airtime_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_T_ALW"/>
        <field name="name">Airtime Allowance</field>
        <field name="code">TAXED_AIRTIME_ALLOWANCE</field>
        <field name="sequence">23</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_airtime_allowance &gt; payslip._rule_parameter('l10n_ke_airtime_alw_max')
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_airtime_allowance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_taxed_pension_allowance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.KE_T_ALW"/>
        <field name="name">Pension Allowance</field>
        <field name="code">TAXED_PENSION_ALLOWANCE</field>
        <field name="sequence">24</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_pension_allowance &gt;= payslip._rule_parameter('l10n_ke_pension_alw_max')
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_pension_allowance - payslip._rule_parameter('l10n_ke_pension_alw_max')
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_taxed_allowance_total" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Taxed Allowance</field>
        <field name="code">TAXED_ALLOWANCE</field>
        <field name="sequence">30</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = categories['KE_T_ALW']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">result = categories['KE_T_ALW']</field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_fixed_bonus" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Fixed Bonus</field>
        <field name="code">BONUS</field>
        <field name="sequence">31</field>
        <field name="condition_select">input</field>
        <field name="condition_other_input_id" ref="l10n_ke_hr_payroll.input_fixed_bonus"/>
        <field name="amount_select">input</field>
        <field name="amount_other_input_id" ref="l10n_ke_hr_payroll.input_fixed_bonus"/>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_fixed_commission" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Fixed Commission</field>
        <field name="code">COMMISSION</field>
        <field name="sequence">31</field>
        <field name="condition_select">input</field>
        <field name="condition_other_input_id" ref="l10n_ke_hr_payroll.input_fixed_commission"/>
        <field name="amount_select">input</field>
        <field name="amount_other_input_id" ref="l10n_ke_hr_payroll.input_fixed_commission"/>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <!-- GROSS -->
    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_gross_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.GROSS"/>
        <field name="name">Taxable Salary</field>
        <field name="sequence">34</field>
        <field name="code">GROSS</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['BASIC'] + categories['ALW']
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_pension_contribution" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Pension Contribution</field>
        <field name="code">PENSION</field>
        <field name="sequence">35</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_pension_contribution
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_gross_taxable" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Mortgage Interest</field>
        <field name="code">MORTGAGE</field>
        <field name="sequence">33</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = employee.l10n_ke_mortgage and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = employee.l10n_ke_mortgage
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <!-- INCOME TAX -->

    <record id="l10n_ke_employees_salary_nssf_tier_1_hidden" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.HIDDEN"/>
        <field name="name">NSSF Tier 1 Employee Deduction</field>
        <field name="code">NSSF_EMPLOYEE_TIER_1</field>
        <field name="sequence">37</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
lower_limit = payslip._rule_parameter('l10n_ke_nssf_lower_limit')
pensionable = categories['BASIC'] + categories['COMMISSIONS']
result = min(lower_limit, pensionable)
result_rate = 6
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_ke_employees_salary_nssf_tier_2_hidden" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.HIDDEN"/>
        <field name="name">NSSF Tier 2 Employee Deduction</field>
        <field name="code">NSSF_EMPLOYEE_TIER_2</field>
        <field name="sequence">38</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
lower_limit = payslip._rule_parameter('l10n_ke_nssf_lower_limit')
upper_limit = payslip._rule_parameter('l10n_ke_nssf_upper_limit')
pensionable = categories['BASIC'] + categories['COMMISSION']
result = 0
if upper_limit &lt; pensionable:
    result = (upper_limit - lower_limit)
elif lower_limit &lt; pensionable:
    result = (pensionable - lower_limit)
result_rate = 6
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <!-- RELIEFS -->

    <record id="l10n_ke_employees_salary_nhif_amount_hidden" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.HIDDEN"/>
        <field name="name">NHIF Amount (Hidden)</field>
        <field name="code">NHIF_AMOUNT_HIDDEN</field>
        <field name="sequence">40</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary and payslip._rule_parameter('l10n_ke_nhif')</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
basic = categories['BASIC']
result = 0
for high, rate in payslip._rule_parameter('l10n_ke_nhif'):
    if basic &lt; float(high):
        result = rate
        break
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_ke_employees_nhif_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.INS_RELIEF"/>
        <field name="name">NHIF Relief</field>
        <field name="code">NHIF_RELIEF</field>
        <field name="sequence">41</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = result_rules['NHIF_AMOUNT_HIDDEN']['total'] and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = result_rules['NHIF_AMOUNT_HIDDEN']['total']
result_rate = -15
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_med_insurance_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.INS_RELIEF"/>
        <field name="name">Medical Insurance Relief</field>
        <field name="code">MED_INSURANCE_RELIEF</field>
        <field name="sequence">43</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_voluntary_medical_insurance and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_voluntary_medical_insurance
result_rate = -15
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_life_insurance_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.INS_RELIEF"/>
        <field name="name">Life Insurance Relief</field>
        <field name="code">LIFE_INSURANCE_RELIEF</field>
        <field name="sequence">44</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_life_insurance and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_life_insurance
result_rate = -15
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_education_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.INS_RELIEF"/>
        <field name="name">Education Relief</field>
        <field name="code">EDUCATION_RELIEF</field>
        <field name="sequence">45</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_education and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_education
result_rate = -15
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_ahl_amount" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.STATUTORY_DED"/>
        <field name="name">AHL Amount</field>
        <field name="code">AHL_AMOUNT</field>
        <field name="sequence">46</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['BASIC'] - employee.disabled * payslip._rule_parameter('l10n_ke_disability_threshold')
result_rate = payslip._rule_parameter('l10n_ke_ahl_employee') * 100
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_ahl_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.RELIEF"/>
        <field name="name">AHL Relief</field>
        <field name="code">AHL_RELIEF</field>
        <field name="sequence">47</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = payslip.date_from &gt;= datetime(2024, 3, 19).date() and payslip.date_to &lt; datetime(2024,12,27).date() and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
if payslip._rule_parameter('l10n_ke_ahl_relief_cap') &lt; result_rules['AHL_AMOUNT']['total'] * payslip._rule_parameter('l10n_ke_ahl_relief_rate'):
    result = - payslip._rule_parameter('l10n_ke_ahl_relief_cap')
else:
    result = result_rules['AHL_AMOUNT']['total']
    result_rate = - payslip._rule_parameter('l10n_ke_ahl_relief_rate') * 100
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_insurance_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.RELIEF"/>
        <field name="name">Insurance Relief</field>
        <field name="code">INSURANCE_RELIEF</field>
        <field name="sequence">48</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = categories['INS_RELIEF'] and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = - min(
    payslip._rule_parameter('l10n_ke_insurance_relief_max'),
    -categories['INS_RELIEF']
)
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="color">#00A09D</field>
    </record>

    <record id="l10n_ke_employees_salary_personal_relief" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.RELIEF"/>
        <field name="name">Personal Relief</field>
        <field name="code">PERS_RELIEF</field>
        <field name="sequence">49</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = payslip._rule_parameter('l10n_ke_personal_relief') and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = - payslip._rule_parameter('l10n_ke_personal_relief')
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <!-- DEDUCTIONS -->

    <record id="l10n_ke_employees_nssf_amount" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.STATUTORY_DED"/>
        <field name="name">NSSF Amount</field>
        <field name="code">NSSF_AMOUNT</field>
        <field name="sequence">53</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = result_rules['NSSF_EMPLOYEE_TIER_1']['total'] + result_rules['NSSF_EMPLOYEE_TIER_2']['total']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="True"/>
    </record>

    <record id="l10n_ke_employees_salary_nhif_amount" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.STATUTORY_DED"/>
        <field name="name">NHIF Amount</field>
        <field name="code">NHIF_AMOUNT</field>
        <field name="sequence">54</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary and result_rules['NHIF_AMOUNT_HIDDEN']['total']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = result_rules['NHIF_AMOUNT_HIDDEN']['total']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_shif_amount" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.STATUTORY_DED"/>
        <field name="name">SHIF Amount</field>
        <field name="code">SHIF_AMOUNT</field>
        <field name="sequence">54</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary and payslip._rule_parameter('l10n_ke_shif_rate')</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
gross = result_rules['GROSS']['total']
result = 0
if gross &lt; float(payslip._rule_parameter('l10n_ke_shif_min_gross')):
    result = payslip._rule_parameter('l10n_ke_shif_min_amount')
else:
    result = payslip._rule_parameter('l10n_ke_shif_rate') * gross
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_gross_taxable" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.GROSS"/>
        <field name="name">Gross Taxable</field>
        <field name="code">GROSS_TAXABLE</field>
        <field name="sequence">55</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
total_pension = result_rules['NSSF_AMOUNT']['total'] + version.l10n_ke_pension_contribution
max_pension_contribution = payslip._rule_parameter('l10n_ke_max_pension_contribution')
non_taxable_pension = min(total_pension, max_pension_contribution) - max(0, version.l10n_ke_pension_allowance - payslip._rule_parameter('l10n_ke_pension_alw_max'))
result = result_rules['GROSS']['total'] - categories['KE_UT_ALW'] - employee.l10n_ke_mortgage - non_taxable_pension
if payslip.date_to &gt;= datetime(2024,12,27).date():
    result -= result_rules['SHIF_AMOUNT']['total'] + result_rules['AHL_AMOUNT']['total']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_income_tax" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Income Tax</field>
        <field name="code">INCOME_TAX</field>
        <field name="sequence">56</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
if version.l10n_ke_is_secondary:
    result = remaining_gross
    result_rate = payslip._rule_parameter('l10n_ke_income_tax_secondary')
else:
    remaining_gross = result_rules['GROSS_TAXABLE']['total']
    income_tax_breakdown = payslip._rule_parameter('l10n_ke_income_tax_breakdown')
    income_tax = 0
    for amount, coefficient in income_tax_breakdown:
        deduced_fraction = min(float(amount), remaining_gross)
        income_tax += deduced_fraction * coefficient
        remaining_gross -= deduced_fraction
        if remaining_gross &lt;= 0:
            break
    result = income_tax
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_salary_paye" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.STATUTORY_DED"/>
        <field name="name">P.A.Y.E.</field>
        <field name="code">PAYE</field>
        <field name="sequence">57</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = result_rules['GROSS_TAXABLE']['total'] &gt;= 24000</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = max(result_rules['INCOME_TAX']['total'] + categories['RELIEF'], 0)
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_statutory_deduction" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.TOTAL_DED"/>
        <field name="name">Statutory Deduction</field>
        <field name="code">STATUTORY_DED</field>
        <field name="sequence">60</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = categories['STATUTORY_DED']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['STATUTORY_DED']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="color">#00A09D</field>
    </record>

    <record id="l10n_ke_employees_helb" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.OTHER_DED"/>
        <field name="name">HELB</field>
        <field name="code">HELB</field>
        <field name="sequence">61</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = 'HELB' in inputs and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">input</field>
        <field name="amount_other_input_id" ref="l10n_ke_hr_payroll.input_helb"/>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_volontary_medical_insurance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.OTHER_DED"/>
        <field name="name">Voluntary Medical Insurance</field>
        <field name="code">MED_INSURANCE</field>
        <field name="sequence">62</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_voluntary_medical_insurance and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_voluntary_medical_insurance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_life_insurance" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.OTHER_DED"/>
        <field name="name">Life Insurance</field>
        <field name="code">LIFE_INSURANCE</field>
        <field name="sequence">63</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_life_insurance and not version.l10n_ke_is_li_managed_by_employee and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_life_insurance
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_education" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.OTHER_DED"/>
        <field name="name">Education</field>
        <field name="code">EDUCATION</field>
        <field name="sequence">64</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_education and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = version.l10n_ke_education
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_other_deductions" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.TOTAL_DED"/>
        <field name="name">Other Deductions</field>
        <field name="code">OTHER_DED</field>
        <field name="sequence">70</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = categories['OTHER_DED']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['OTHER_DED']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="color">#00A09D</field>
    </record>

    <record id="l10n_ke_employees_salary_pension_contribution" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.TOTAL_DED"/>
        <field name="name">Pension Contribution</field>
        <field name="code">PENSION</field>
        <field name="sequence">72</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = version.l10n_ke_pension_contribution and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">percentage</field>
        <field name="amount_percentage_base">version.l10n_ke_pension_contribution</field>
        <field name="amount_percentage">100</field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_fringe_benefit" model="hr.salary.rule">
        <field name="category_id" ref="l10n_ke_hr_payroll.OTHER_DED"/>
        <field name="name">Fringe Benefit</field>
        <field name="code">FRINGE_BENEFIT</field>
        <field name="sequence">73</field>
        <field name="condition_select">python</field>
        <field name="condition_python">
result = 'FRINGE_BENEFIT' in inputs and not version.l10n_ke_is_secondary
        </field>
        <field name="amount_select">input</field>
        <field name="amount_other_input_id" ref="l10n_ke_hr_payroll.input_fringe_benefit"/>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_employees_total_deductions" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Total Deductions</field>
        <field name="code">TOTAL_DED</field>
        <field name="sequence">80</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = categories['TOTAL_DED']</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['TOTAL_DED']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <!-- NET -->
    <record id="l10n_ke_employer_nita" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">NITA Employer Cost</field>
        <field name="code">NITA</field>
        <field name="sequence">110</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = payslip._rule_parameter('l10n_ke_nita')
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_ke_employer_nssf_employer" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">NSSF Employer Cost</field>
        <field name="code">NSSF_EMP</field>
        <field name="sequence">120</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = not version.l10n_ke_is_secondary</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = result_rules['NSSF_AMOUNT']['total']
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_ke_employer_salary_ahl_amount" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">AHL Amount (employer)</field>
        <field name="code">AHL_AMOUNT_EMP</field>
        <field name="sequence">121</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['BASIC'] - employee.disabled * payslip._rule_parameter('l10n_ke_disability_threshold')
result_rate = payslip._rule_parameter('l10n_ke_ahl_employer') * 100
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_ke_employees_non_cash" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">Non-Cash Benefit</field>
        <field name="code">NON_CASH_BENEFIT</field>
        <field name="sequence">130</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'NON_CASH_BENEFIT' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = inputs['NON_CASH_BENEFIT'].amount
result_name = inputs['NON_CASH_BENEFIT'].name
        </field>
        <field name="struct_id" ref="l10n_ke_hr_payroll.hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_attachment_of_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Attachment of Salary</field>
        <field name="sequence">174</field>
        <field name="code">ATTACH_SALARY</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'ATTACH_SALARY' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['ATTACH_SALARY'].amount
result_name = inputs['ATTACH_SALARY'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_assignment_of_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Assignment of Salary</field>
        <field name="sequence">174</field>
        <field name="code">ASSIG_SALARY</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'ASSIG_SALARY' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['ASSIG_SALARY'].amount
result_name = inputs['ASSIG_SALARY'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_child_support" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Child Support</field>
        <field name="code">CHILD_SUPPORT</field>
        <field name="amount_select">code</field>
        <field name="sequence">174</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'CHILD_SUPPORT' in inputs</field>
        <field name="amount_python_compute">
result = -inputs['CHILD_SUPPORT'].amount
result_name = inputs['CHILD_SUPPORT'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_deduction_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Deduction</field>
        <field name="sequence">198</field>
        <field name="code">DEDUCTION</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'DEDUCTION' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['DEDUCTION'].amount
result_name = inputs['DEDUCTION'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_reimbursement_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Reimbursement</field>
        <field name="sequence">199</field>
        <field name="code">REIMBURSEMENT</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'REIMBURSEMENT' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = inputs['REIMBURSEMENT'].amount
result_name = inputs['REIMBURSEMENT'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>

    <record id="l10n_ke_hr_payroll_structure_ken_employee_salary_net_salary" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">Net Salary</field>
        <field name="sequence">200</field>
        <field name="code">NET</field>
        <field name="appears_on_employee_cost_dashboard" eval="True"/>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = result_rules['GROSS']['total'] - result_rules['TOTAL_DED']['total'] + categories['EXEMPTION'] + result_rules['NON_CASH_BENEFIT']['total']
        </field>
        <field name="struct_id" ref="hr_payroll_structure_ken_employee_salary"/>
    </record>
</odoo>
