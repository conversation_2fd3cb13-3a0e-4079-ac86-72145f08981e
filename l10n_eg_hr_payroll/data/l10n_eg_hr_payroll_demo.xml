<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_eg" model="res.partner" forcecreate="1">
        <field name="name">My Egyptian Company</field>
        <field name="country_id" ref="base.eg"/>
    </record>

    <record id="base.demo_company_eg" model="res.company" forcecreate="1">
        <field name="name">My Egyptian Company</field>
        <field name="partner_id" ref="base.partner_demo_company_eg"/>
        <field name="currency_id" ref="base.EGP"/>
        <field name="street">Egyptian Street</field>
        <field name="zip">1000</field>
        <field name="city">Cairo</field>
        <field name="country_id" ref="base.eg"/>
        <field name="resource_calendar_id" ref="l10n_eg_hr_payroll.resource_calendar_def_40h"/>
    </record>

    <record id="base.user_admin" model="res.users">
        <field name="company_ids" eval="[(4, ref('base.demo_company_eg'))]"/>
    </record>
    <record id="base.user_demo" model="res.users">
        <field name="company_ids" eval="[(4, ref('base.demo_company_eg'))]"/>
    </record>

    <record id="l10n_eg_hr_payroll.resource_calendar_def_40h" model="resource.calendar">
        <field name="company_id" ref="base.demo_company_eg"/>
    </record>

    <record id="eg_hr_department_marketing" model="hr.department">
        <field name="name">Marketing EG</field>
        <field name="company_id" ref="base.demo_company_eg"/>
    </record>
    <record id="eg_hr_department_accounting" model="hr.department">
        <field name="name">Accounting EG</field>
        <field name="company_id" ref="base.demo_company_eg"/>
    </record>

    <record id="res_partner_abeer" model="res.partner">
        <field name="name">Abeer Ehab Abdullah</field>
        <field name="street">New Cairo 1</field>
        <field name="city">Cairo</field>
        <field name="zip">11511</field>
        <field name="state_id" ref="base.state_eg_c"/>
        <field name="country_id" ref="base.eg"/>
        <field name="phone">02 1234 5678</field>
        <field name="company_id" ref="base.demo_company_eg"/>
    </record>
    <record id="res_partner_abeer" model="res.partner">
        <field name="email" model="res.partner" eval="'demo.' + obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).name.split(' ')[-1].lower() + '@example.com'"/>
    </record>

    <record id="res_partner_hany" model="res.partner">
        <field name="name">Hany Ahmed Magdy</field>
        <field name="street">Egyptian Street</field>
        <field name="city">Cairo</field>
        <field name="zip">4272051</field>
        <field name="state_id" ref="base.state_eg_c"/>
        <field name="country_id" ref="base.eg"/>
        <field name="phone">02 2345 6789</field>
        <field name="company_id" ref="base.demo_company_eg"/>
    </record>
    <record id="res_partner_hany" model="res.partner">
        <field name="email" model="res.partner" eval="'demo.' + obj(ref('l10n_eg_hr_payroll.res_partner_hany')).name.split(' ')[-1].lower() + '@example.com'"/>
    </record>

    <record id="user_hany" model="res.users">
        <field name="partner_id" ref="l10n_eg_hr_payroll.res_partner_hany"/>
        <!-- <EMAIL> -->
        <field name="login" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).name.replace(' ', '.').lower() + '@example.com'"/>
        <field name="signature">H.A. Magdy</field>
        <field name="company_ids" eval="[(4, ref('base.demo_company_eg'))]"/>
        <field name="company_id" ref="base.demo_company_eg"/>
        <field name="group_ids" eval="[(6,0,[ref('base.group_user')])]"/>
        <field name="image_1920" type="base64" file="hr/static/img/employee_ngh-image.jpg"/>
    </record>

    <record id="eg_res_bank_national" model="res.bank">
        <field name="name">National Bank of Egypt</field>
        <field name="bic">NBEGEGCXXXX</field>
        <field name="street">National Bank of Egypt street</field>
        <field name="city">Cairo</field>
        <field name="zip">9439004</field>
        <field name="state" ref="base.state_eg_c"/>
        <field name="country" ref="base.eg"/>
        <field name="phone">02 4567 8901</field>
    </record>
    <record id="eg_res_bank_national" model="res.bank">
        <field name="email" model="res.bank" eval="obj(ref('l10n_eg_hr_payroll.eg_res_bank_national')).name.replace(' ', '.').lower() + '@example.com'"/>
    </record>

    <record id="eg_res_bank_suez_canal" model="res.bank">
        <field name="name">Suez Canal Bank</field>
        <field name="bic">SUCAEGCXXXX</field>
        <field name="street">Suez Canal Bank street</field>
        <field name="city">Cairo</field>
        <field name="zip">123004</field>
        <field name="state" ref="base.state_eg_c"/>
        <field name="country" ref="base.eg"/>
        <field name="phone">02 7689 0123</field>
    </record>
    <record id="eg_res_bank_suez_canal" model="res.bank">
        <field name="email" model="res.bank" eval="obj(ref('l10n_eg_hr_payroll.eg_res_bank_suez_canal')).name.replace(' ', '.').lower() + '@example.com'"/>
    </record>

    <record id="l10n_eg_res_partner_bank_account_abeer" model="res.partner.bank">
        <field name="acc_number">*****************************</field>
        <field name="allow_out_payment" eval="True"/>
        <field name="bank_id" ref="eg_res_bank_national"/>
        <field name="partner_id" ref="l10n_eg_hr_payroll.res_partner_abeer"/>
        <field name="company_id" ref="base.demo_company_eg"/>
        <field name="currency_id" ref="base.EGP"/>
    </record>

    <record id="l10n_eg_res_partner_bank_account_hany" model="res.partner.bank">
        <field name="acc_number">*****************************</field>
        <field name="allow_out_payment" eval="True"/>
        <field name="bank_id" ref="eg_res_bank_suez_canal"/>
        <field name="partner_id" ref="l10n_eg_hr_payroll.res_partner_hany"/>
        <field name="company_id" ref="base.demo_company_eg"/>
        <field name="currency_id" ref="base.EGP"/>
    </record>

    <record id="hr_employee_abeer" model="hr.employee">
        <field name="name" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).name"/>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="image_1920" type="base64" file="hr/static/img/employee_mit-image.jpg"/>
        <field name="work_phone">99 1234 5678</field>
        <field name="work_email" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).name.replace(' ', '.').lower() + '@example.com'"/>
        <field name="work_contact_id" ref="l10n_eg_hr_payroll.res_partner_abeer"/>
        <field name="company_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).company_id"/>
        <field name="resource_calendar_id" ref="l10n_eg_hr_payroll.resource_calendar_def_40h"/>
        <field name="tz">Africa/Cairo</field>
        <field name="private_street" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).street"/>
        <field name="private_city" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).city"/>
        <field name="private_zip" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).zip"/>
        <field name="private_state_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).state_id"/>
        <field name="private_country_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).country_id"/>
        <field name="private_email" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).email"/>
        <field name="private_phone" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).phone"/>
        <field name="distance_home_work">17</field>
        <field name="country_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).country_id"/>
        <field name="identification_id">98309120123456</field>
        <field name="sex">female</field>
        <field name="birthday" eval="datetime(1983, 9, 12).date()"/>
        <field name="place_of_birth">Cairo</field>
        <field name="country_of_birth" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_abeer')).country_id"/>
        <field name="emergency_contact">Mona Haddad</field>
        <field name="emergency_phone">99 4729 1200</field>
        <field name="certificate">master</field>
        <field name="study_field">Engineering</field>
        <field name="study_school">Cairo University</field>
        <field name="marital">single</field>
        <field name="department_id" ref="eg_hr_department_marketing"/>
        <field name="wage">35000</field>
        <field name="hr_responsible_id" ref="base.user_demo"/>
        <field name="contract_date_start" eval="DateTime.today() + relativedelta(months=-4, days=-10)"/>
        <field name="contract_date_end" eval="DateTime.today() + relativedelta(months=2, days=-10)"/>
        <field name="contract_type_id" ref="hr.contract_type_temporary"/>
        <field name="structure_type_id" ref="structure_type_employee_eg"/>
        <field name="l10n_eg_housing_allowance">1000</field>
        <field name="l10n_eg_other_allowances">1000</field>
        <field name="bank_account_id" ref="l10n_eg_hr_payroll.l10n_eg_res_partner_bank_account_abeer"/>
    </record>

    <record id="hr_employee_hany" model="hr.employee">
        <field name="name" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).name"/>
        <field name="job_id" ref="hr.job_consultant"/>
        <field name="image_1920" type="base64" file="hr/static/img/employee_ngh-image.jpg"/>
        <field name="work_phone">99 0011 2345</field>
        <field name="work_email" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).name.replace(' ', '.').lower() + '@example.com'"/>
        <field name="parent_id" ref="l10n_eg_hr_payroll.hr_employee_abeer"/>
        <field name="user_id" ref="l10n_eg_hr_payroll.user_hany"/>
        <field name="company_id" ref="base.demo_company_eg"/>
        <field name="resource_calendar_id" ref="l10n_eg_hr_payroll.resource_calendar_def_40h"/>
        <field name="tz">Africa/Cairo</field>
        <field name="private_street" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).street"/>
        <field name="private_city" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).city"/>
        <field name="private_zip" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).zip"/>
        <field name="private_state_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).state_id"/>
        <field name="private_country_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).country_id"/>
        <field name="private_email" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).email"/>
        <field name="private_phone" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).phone"/>
        <field name="distance_home_work">47</field>
        <field name="country_id" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).country_id"/>
        <field name="identification_id">99812030123456</field>
        <field name="sex">male</field>
        <field name="birthday" eval="datetime(1998, 12, 3).date()"/>
        <field name="place_of_birth">Cairo</field>
        <field name="country_of_birth" model="res.partner" eval="obj(ref('l10n_eg_hr_payroll.res_partner_hany')).country_id"/>
        <field name="emergency_contact">Mostafa Yussef</field>
        <field name="emergency_phone">99 4729 1234</field>
        <field name="certificate">master</field>
        <field name="study_field">Psychology</field>
        <field name="study_school">Alexandira University</field>
        <field name="marital">single</field>
        <field name="department_id" ref="eg_hr_department_accounting"/>
        <field name="wage">40000</field>
        <field name="hr_responsible_id" ref="base.user_demo"/>
        <field name="date_version" eval="DateTime(DateTime.today().year-3,month=2,day=25)"/>
        <field name="contract_date_start" eval="DateTime(DateTime.today().year-3,month=2,day=25)"/>
        <field name="contract_date_end" eval="DateTime(DateTime.today().year-2,month=2,day=25)"/>
        <field name="contract_type_id" ref="hr.contract_type_full_time"/>
        <field name="structure_type_id" ref="structure_type_employee_eg"/>
        <field name="l10n_eg_housing_allowance">1000</field>
        <field name="l10n_eg_other_allowances">1000</field>
        <field name="bank_account_id" ref="l10n_eg_hr_payroll.l10n_eg_res_partner_bank_account_hany"/>
    </record>

    <function name="create_version" model="hr.employee">
        <value eval="[ref('l10n_eg_hr_payroll.hr_employee_hany')]"/>
        <value eval="{
            'date_version': DateTime(DateTime.today().year-2,month=2,day=25) + relativedelta(days=1),
            'contract_date_start': DateTime(DateTime.today().year-2,month=2,day=25) + relativedelta(days=1),
            'contract_date_end': False,
            'contract_type_id': ref('hr.contract_type_permanent'),
            'structure_type_id': ref('structure_type_employee_eg'),
            'wage': 42000.00
        }"/>
    </function>
</odoo>
