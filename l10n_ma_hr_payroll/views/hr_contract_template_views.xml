<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_contract_template_view_form" model="ir.ui.view">
            <field name="name">hr.contract.template.view.form.inherit.l10_ma_hr_payroll</field>
            <field name="model">hr.version</field>
            <field name="priority">20</field>
            <field name="inherit_id" ref="hr.hr_contract_template_form_view"/>
            <field name="arch" type="xml">
                <group name="salary_info" position="inside">
                    <group name="untaxed" string="Moroccan Localization"
                        invisible="country_code != 'MA'">
                        <field name="l10n_ma_hra"
                            class="o_hr_narrow_field"/>
                        <field name="l10n_ma_da"
                            class="o_hr_narrow_field"/>
                        <field name="l10n_ma_meal_allowance"
                            class="o_hr_narrow_field"/>
                        <field name="l10n_ma_medical_allowance"
                            class="o_hr_narrow_field"/>
                        <field name="l10n_ma_kilometric_exemption"
                            class="o_hr_narrow_field"/>
                        <field name="l10n_ma_transport_exemption"
                            class="o_hr_narrow_field"/>
                    </group>
                </group>
            </field>
        </record>
    </data>
</odoo>
