<?xml version="1.0" encoding="utf-8"?>
<templates>

  <div t-name="spreadsheet_dashboard_edition.DashboardEdit">
    <button t-if="isDashboardAdmin"
            type="button"
            class="btn btn-link o_edit_dashboard fa fa-pencil"
            t-att-class="props.data.is_from_data ? 'btn-warning' : ''"
            tabindex="-1"
            draggable="false"
            aria-label="Edit"
            t-att-data-tooltip="tooltip"
            t-on-click.stop="onClick" />
  </div>
</templates>
