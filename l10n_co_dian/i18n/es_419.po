# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_co_dian
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-07 17:26+0000\n"
"PO-Revision-Date: 2025-01-21 14:44-0600\n"
"Last-Translator: mfar\n"
"Language-Team: \n"
"Language: es_419\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.4\n"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "1018418009"
msgstr "1018418009"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "18760000001"
msgstr "18760000001"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "2019-01-01"
msgstr "2019-01-01"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "2024-03-15 15:43:50"
msgstr "2024-03-15 15:43:50"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "2024-03-15 15:43:58"
msgstr "2024-03-15 15:43:58"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "2030-12-31"
msgstr "2030-12-31"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "4390"
msgstr "4390"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid ""
"6c33aba6cd0086c0af774f5ff5e3535e16dc9a753df6bdd8da3d5d242ccee7016d4fc897225593b6e240a9e9f8b19373"
msgstr ""
"6c33aba6cd0086c0af774f5ff5e3535e16dc9a753df6bdd8da3d5d242ccee7016d4fc897225593b6e240a9e9f8b19373"

#. module: l10n_co_dian
#: model:mail.template,body_html:l10n_co_dian.email_template_edi_credit_note
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is your\n"
"        <t t-if=\"object.name\">\n"
"            credit note <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">RINV/2021/05/0001</span>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            credit note\n"
"        </t>\n"
"        <t t-if=\"object.invoice_origin\">\n"
"            (with reference: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 143,750.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Apreciable\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"        </t>\n"
"        <br/><br/>\n"
"        Esta es su\n"
"        <t t-if=\"object.name\">\n"
"            nota de crédito <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">RINV/2021/05/0001</span>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            nota de crédito\n"
"        </t>\n"
"        <t t-if=\"object.invoice_origin\">\n"
"            (con referencia: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"        </t>\n"
"        por un importe total de <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$143,750.00</span>\n"
"        de <t t-out=\"object.company_id.name or ''\">SuEmpresa</t>.\n"
"        <br/><br/>\n"
"        No dude en contactarnos si tiene alguna pregunta.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: l10n_co_dian
#: model:mail.template,body_html:l10n_co_dian.email_template_edi_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is your\n"
"        <t t-if=\"object.name\">\n"
"            invoice <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">INV/2021/05/0005</span>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            invoice\n"
"        </t>\n"
"        <t t-if=\"object.invoice_origin\">\n"
"            (with reference: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 143,750.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <t t-if=\"object.payment_state in ('paid', 'in_payment')\">\n"
"            This invoice is already paid.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Please remit payment at your earliest convenience.\n"
"            <t t-if=\"object.payment_reference\">\n"
"                <br/><br/>\n"
"                Please use the following communication for your payment: <strong t-out=\"object.payment_reference or ''\">INV/2021/05/0005</strong>\n"
"                <t t-if=\"object.partner_bank_id\">\n"
"                    on the account <strong t-out=\"object.partner_bank_id.acc_number\"/>\n"
"                </t>\n"
"                .\n"
"            </t>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object, 'timesheet_count') and object.timesheet_count\">\n"
"            <br/><br/>\n"
"            PS: you can review your timesheets <a t-att-href=\"'/my/timesheets?search_in=invoice&amp;search=%s' % object.name\">from the portal.</a>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Apreciable\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"        </t>\n"
"        <br/><br/>\n"
"        Esta es su\n"
"        <t t-if=\"object.name\">\n"
"            factura <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">INV/2021/05/0005</span>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            factura\n"
"        </t>\n"
"        <t t-if=\"object.invoice_origin\">\n"
"            (con referencia: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"        </t>\n"
"        por un importe total de <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$143,750.00</span>\n"
"        de <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <t t-if=\"object.payment_state in ('paid', 'in_payment')\">\n"
"            Esta factura ya está pagada.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Realice su pago lo más pronto posible.\n"
"            <t t-if=\"object.payment_reference\">\n"
"                <br/><br/>\n"
"                Utilice la siguiente referencia para realizar su pago: <strong t-out=\"object.payment_reference or ''\">INV/2021/05/0005</strong>\n"
"                <t t-if=\"object.partner_bank_id\">\n"
"                    en la cuenta <strong t-out=\"object.partner_bank_id.acc_number\"/>\n"
"                </t>\n"
"                .\n"
"            </t>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object, 'timesheet_count') and object.timesheet_count\">\n"
"            <br/><br/>\n"
"            Recuerde que puede consultar sus hojas de horas <a t-att-href=\"'/my/timesheets?search_in=invoice&amp;search=%s' % object.name\">desde el portal.</a>\n"
"        </t>\n"
"        <br/><br/>\n"
"        No dude en contactarnos si tiene alguna pregunta.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\" data-o-mail-quote-container=\"1\">\n"
"            <br data-o-mail-quote=\"1\"/><br data-o-mail-quote=\"1\"/>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "<strong>Payment Means</strong>"
msgstr "<strong>Método de pago</strong>"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "<strong>Payment Method</strong>"
msgstr "<strong>Términos de pago</strong>"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "A posted time is required to compute the CUFE/CUDE/CUDS."
msgstr ""
"Es necesario contar con la hora de publicación para calcular el "
"CUFE/CUDE/CUDS."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "A technical key on the journal is required to compute the CUFE."
msgstr ""
"Es necesario configurar una llave técnica en el diario para generar el CUFE."

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_state__invoice_accepted
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_document__state__invoice_accepted
msgid "Accepted"
msgstr "Aceptado"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_account_move_send
msgid "Account Move Send"
msgstr "Documento enviado"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_certification_process
msgid "Activate the certification process"
msgstr "Activar proceso de certificación"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_res_config_settings__l10n_co_dian_certification_process
msgid ""
"Activate this checkbox if you are in the certification process with the "
"DIAN."
msgstr ""
"Active esta casilla si está en el proceso de certificación con la DIAN."

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_res_config_settings__l10n_co_dian_demo_mode
msgid "Activate this checkbox if you’re testing elecronic invoice flows with internal validation."
msgstr "Activa esta casilla si estás probando los flujos de facturación electrónica con validaciones internas."

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_res_config_settings__l10n_co_dian_test_environment
msgid ""
"Activate this checkbox if you’re testing workflows for electronic invoicing."
msgstr ""
"Active esta casilla si está probando los flujos de trabajo para la "
"facturación electrónica."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid "Activate this mode to be able to test electronic invoicing only with internal validations, but no responce from the DIAN environment."
msgstr "Activa este modo para poder probar los flujos de facturación electrónica solo con validaciones internas, pero sin respuesta del entorno de la DIAN."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid "Add your certificates to sign your documents."
msgstr "Agregue sus certificados para firmar sus documentos."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/res_company.py:0
msgid "All Documents"
msgstr "Todos los Documentos"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "All products in an AIU invoice should be a service."
msgstr "Todos los productos de una factura AIU debem de ser de tipo servicio."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/xml_utils.py:0
msgid "Ambiguous reference URI %(uri)s resolved to %(count)d nodes"
msgstr "La referencia URI %(uri)s ambigua se resolvió en %(count)d nodos"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "ApplicationResponse"
msgstr "ApplicationResponse"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.l10n_co_dian_operation_mode_view_tree
msgid "Are you sure you want to delete this record?"
msgstr "¿Está seguro de que desea eliminar este registro?"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid ""
"Alcohol percentage should be set on the %(field_description)s field"
" for product: %(product_name)s when using ICL taxes."
msgstr "El porcentaje de alcohol debe establecerse en el campo del %(field_description)s"
" para el producto: %(product_name)s al usar impuestos ICL."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "An Electronic Invoice Type must be selected before sending the invoice."
msgstr "Debe seleccionar un Tipo de Documento Electrónico antes de enviar la factura."

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__attachment_id
msgid "Attachment"
msgstr "Adjunto"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Autorretenedor"
msgstr "Autorretenedor"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Barcode"
msgstr "Código de barras"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid "Begin Certification Process"
msgstr "Iniciar Proceso de Habilitaciòn"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_identifier_type__cude
msgid "CUDE"
msgstr "CUDE"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_identifier_type__cuds
msgid "CUDS"
msgstr "CUDS"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_identifier_type__cufe
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "CUFE"
msgstr "CUFE"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_edi_cufe_cude_ref
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_edi_cufe_cude_ref
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__identifier
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_invoice_filter_inherit_l10n_co_dian
msgid "CUFE/CUDE/CUDS"
msgstr "CUFE/CUDE/CUDS"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__res_company__l10n_co_dian_provider__carvajal
msgid "Carvajal"
msgstr "Carvajal"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_certificate_certificate
msgid "Certificate"
msgstr "Certificado"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/xml_utils.py:0
msgid "Certificate is not available"
msgstr "El certificado no está disponible"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid "Certificates"
msgstr "Certificados"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_res_config_settings__l10n_co_dian_certificate_ids
msgid "Certificates to be used for electronic invoicing."
msgstr "Certificados a utilizar para la facturación electrónica."

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__certification_process
msgid "Certification Process"
msgstr "Proceso de certificación"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/res_company.py:0
msgid "Certification Process Has Concluded"
msgstr "El Proceso de Habilitación ha Concluido"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_l10n_co_dian_document
msgid "Colombian documents used for each interaction with the DIAN"
msgstr "Documentos colombianos utilizados para cada interacción con la DIAN"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_l10n_co_dian_operation_mode
msgid "Colombian operation modes of DIAN used for different documents"
msgstr ""
"Modos de operación colombianos de la DIAN utilizados para diferentes "
"documentos"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__company_id
msgid "Company"
msgstr "Empresa"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid ""
"Configure the operations modes for the different types of documents to be "
"generated from Odoo"
msgstr ""
"Configure los modos de operación para los distintos tipos de documentos que "
"se generarán desde Odoo"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Contado"
msgstr "Contado"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_account_journal__l10n_co_dian_technical_key
msgid "Control key acquired in the DIAN portal, used to generate the CUFE"
msgstr ""
"Clave de control adquirida en el portal de la DIAN, utilizada para generar "
"el CUFE"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__create_uid
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__create_date
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_co_dian
#: model:mail.template,name:l10n_co_dian.email_template_edi_credit_note
msgid "Credit Note (DIAN): Sending"
msgstr "Nota de crédito (DIAN): Enviando"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Crédito"
msgstr "Crédito"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Crédito ACH"
msgstr "Crédito ACH"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move_send.py:0
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_move_form_inherit_l10n_co_dian
msgid "DIAN"
msgstr "DIAN"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "DIAN 2.1"
msgstr "DIAN 2.1"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_operation_mode__dian_software_operation_mode__invoice
msgid "DIAN 2.1: Electronic Invoices"
msgstr "DIAN 2.1: Facturas electrónicas"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_operation_mode__dian_software_operation_mode__bill
msgid "DIAN 2.1: Support Documents"
msgstr "DIAN 2.1: Documentos soporte"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/l10n_co_dian_operation_mode.py:0
msgid "DIAN Operation Mode"
msgstr "DIAN Modo de operación"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__l10n_co_dian_operation_mode_ids
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_operation_mode_ids
msgid "DIAN Operation Modes"
msgstr "DIAN Modos de operación"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "DIAN resolution"
msgstr "DIAN Resolución"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_journal.py:0
msgid "DIAN returned error %(code)s: \"%(message)s\""
msgstr "La DIAN devolvió el error %(code)s: \"%(message)s\""

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_invoice_filter_inherit_l10n_co_dian
msgid "DIAN status"
msgstr "DIAN Estado"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__res_company__l10n_co_dian_provider__dian
msgid "DIAN: Free service"
msgstr "DIAN: Servicio gratuito"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__datetime
msgid "Datetime"
msgstr "Fecha y hora"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.l10n_co_dian_operation_mode_view_tree
msgid "Delete"
msgstr "Eliminar"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_edi_xml_ubl_dian__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_journal__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move_send__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_mail_template__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "Documentos adjuntos"
msgstr "Documentos adjuntos"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_move_form_inherit_l10n_co_dian
msgid "Download"
msgstr "Descargar"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Economic activity:"
msgstr "Actividad económica:"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Effective date from"
msgstr "Fecha efectiva desde"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_provider
msgid "Electronic Invoicing Provider"
msgstr "Proveedor de facturación electrónica"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_mail_template
msgid "Email Templates"
msgstr "Plantillas de correo electrónico"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/l10n_co_dian_document.py:0
msgid "Error %(code)s when calling the DIAN server: %(response)s"
msgstr "Error %(code)s al llamar al servidor de la DIAN: %(response)s"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move_send.py:0
msgid "Error(s) when generating the Attached Document:"
msgstr "Error(es) al generar el documento adjunto:"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move_send.py:0
msgid "Error(s) when generating the UBL attachment:"
msgstr "Error(es) al momento de generar el adjunto UBL:"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move.py:0
msgid ""
"Error(s) when generating the UBL attachment:\n"
"- %s"
msgstr ""
"Error(es) al momento de generar el adjunto UBL:\n"
"- %s"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move_send.py:0
msgid "Error(s) when sending the document to the DIAN:"
msgstr "Error(es) al enviar el documento a la DIAN:"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move.py:0
msgid ""
"Error(s) when sending the document to the DIAN:\n"
"- %s"
msgstr ""
"Error(es) al enviar el documento a la DIAN:\n"
"- %s"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "Every exportation product must have a brand."
msgstr "Todos los productos de exportación deben tener una marca."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "Every exportation product must have a customs code."
msgstr "Todos los productos de exportación deben tener un código arancelario."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "Every lines should have non zero price units."
msgstr "Todas las líneas deben tener un precio unitario diferente de 0."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Factura de venta"
msgstr "Factura de venta"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_move_form_inherit_l10n_co_dian
msgid "Fetch Attached Document"
msgstr "Obtener documento adjunto"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Generated by: Software Propio"
msgstr "Generado por: Software Propio"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_move_form_inherit_l10n_co_dian
msgid "Get Status"
msgstr "Obtener estado"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_edi_xml_ubl_dian__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_journal__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move_line__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move_send__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_certificate_certificate__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_mail_template__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__id
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_document__zip_key
msgid ""
"ID returned by the DIAN when sending a document with the certification "
"process activated."
msgstr ""
"ID devuelto por la DIAN al enviar un documento con el proceso de "
"certificación activado."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "INV/2023/0001"
msgstr "INV/2023/0001"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "IVA"
msgstr "IVA"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_document__test_environment
msgid "Indicates whether the test endpoint was used to send this document"
msgstr ""
"Indica si se utilizó el punto final de prueba para enviar este documento"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_document__certification_process
msgid ""
"Indicates whether we were in the certification process when sending this "
"document"
msgstr ""
"Indica si estábamos en el proceso de certificación al enviar este documento"

#. module: l10n_co_dian
#: model:mail.template,name:l10n_co_dian.email_template_edi_invoice
msgid "Invoice (DIAN): Sending"
msgstr "Factura (DIAN): Enviando"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/l10n_co_dian_document.py:0
msgid "Invoice is being processed by the DIAN."
msgstr "La DIAN está procesando la factura."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Issuing date:"
msgstr "Fecha de emisión:"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_attachment_id
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_attachment_id
msgid "L10N Co Dian Attachment"
msgstr "L10N Co Dian Adjunto"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__l10n_co_dian_certificate_ids
msgid "L10N Co Dian Certificate"
msgstr "L10N Co Dian Certificado"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__l10n_co_dian_certification_process
msgid "L10N Co Dian Certification Process"
msgstr "L10N Co Dian Proceso de certificación"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_document_ids
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_document_ids
msgid "L10N Co Dian Document"
msgstr "L10N Co Dian Documento"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_identifier_type
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_identifier_type
msgid "L10N Co Dian Identifier Type"
msgstr "L10N Co Dian Tipo de identificación"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_is_enabled
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_is_enabled
msgid "L10N Co Dian Is Enabled"
msgstr "L10N Co Dian Está habilitado"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_post_time
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_post_time
msgid "L10N Co Dian Post Time"
msgstr "L10N Co Dian Hora de publicación"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_journal__l10n_co_dian_provider
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__l10n_co_dian_provider
msgid "L10N Co Dian Provider"
msgstr "L10N Co Dian Proveedor"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_show_support_doc_button
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_show_support_doc_button
msgid "L10N Co Dian Show Support Doc Button"
msgstr "L10N Co Dian Mostrar Botón Documento Soporte"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_bank_statement_line__l10n_co_dian_state
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_move__l10n_co_dian_state
msgid "L10N Co Dian State"
msgstr "L10N Co Dian Estado"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__write_uid
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__write_date
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__message
msgid "Message"
msgstr "Mensaje"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__message_json
msgid "Message Json"
msgstr "JSON del mensaje"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__move_id
msgid "Move"
msgstr "Documento"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "NIT:"
msgstr "NIT:"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "No DIAN Operation Mode Matches"
msgstr "No coinciden los modos de operación de la DIAN"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_journal.py:0
msgid ""
"No matching numbering range found for journal with short code '%(actual)s'. "
"Found '%(expected)s' instead."
msgstr ""
"No se encontró un rango de numeración coincidente para el diario con el "
"código corto ‘%(actual)s’, se encontró ‘%(expected)s’."

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_cert_credit_count
msgid "Number of Credit Notes to Certify"
msgstr "Cantidad de Notas Crédito a Certificar"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_cert_debit_count
msgid "Number of Debit Notes to Certify"
msgstr "Cantidad de Notas Débito a Certificar"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_cert_invoice_count
msgid "Number of Invoices to Certify"
msgstr "Cantidad de Facturas a Certificar"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/res_company.py:0
msgid "Open"
msgstr "Abrir"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.l10n_co_dian_operation_mode_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid "Operation Modes"
msgstr "Modos de operación"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.l10n_co_dian_operation_mode_view_form
msgid "Operation mode"
msgstr "Modo de operación"

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_state__invoice_pending
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_document__state__invoice_pending
msgid "Pending"
msgstr "Pendiente"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/res_company.py:0
msgid "Please click below to check the status of your documents."
msgstr "Dar clic abajo para consultar el estado de tus documentos."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid ""
"Please set a credit note reason as it is required for this type of "
"transaction."
msgstr ""
"Establezca un motivo de nota de crédito, es necesario para este tipo de "
"transacciones."

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_state__invoice_rejected
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_document__state__invoice_rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_journal_form_inherit_l10n_co_dian
msgid "Reload DIAN configuration"
msgstr "Volver a cargar la configuración de la DIAN"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "SETT1"
msgstr "SETT1"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "SETT5000000"
msgstr "SETT5000000"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid ""
"Select between sending electronic documents as an 'In house software' "
"directly to the DIAN portal or with a 'Technology provider' as Carvajal."
msgstr ""
"Seleccione entre enviar documentos electrónicos como “Software propio” "
"directamente al portal de la DIAN o con un “Proveedor tecnológico” como "
"Carvajal."

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_operation_mode
msgid ""
"Select the type of document to be generated with the operation type "
"configured"
msgstr ""
"Seleccione el tipo de documento a generar con el tipo de operación "
"configurado"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_move_form_inherit_l10n_co_dian
msgid "Send Support Document to DIAN"
msgstr "Envíe el documento soporte a la DIAN"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid ""
"Send test electronic documents with your certificate to achieve the "
"'Enabled' status in the DIAN portal for 'in house software' operation mode. "
"In the fields below, define the number of 'Total Required Accepted Documents' "
"specified in the Test Set assigned by the DIAN."
msgstr ""
"Envíe documentos electrónicos de prueba con su certificado para lograr el "
"estado 'Habilitado' en el portal de la DIAN para el modo de operación "
"'Software propio'. En los campos de abajo, defina la cantidad de "
"'Total de Documentos Aceptados Requeridos' especificados en el "
"Set de Pruebas asignado por la DIAN."

#. module: l10n_co_dian
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__account_move__l10n_co_dian_state__invoice_sending_failed
#: model:ir.model.fields.selection,name:l10n_co_dian.selection__l10n_co_dian_document__state__invoice_sending_failed
msgid "Sending Failed"
msgstr "Envío fallido"

#. module: l10n_co_dian
#: model:mail.template,description:l10n_co_dian.email_template_edi_credit_note
msgid "Sent to customers with the credit note in attachment"
msgstr "Enviado a los clientes con la nota de crédito adjunta"

#. module: l10n_co_dian
#: model:mail.template,description:l10n_co_dian.email_template_edi_invoice
msgid "Sent to customers with their invoices in attachment"
msgstr "Enviado a los clientes con las facturas adjuntas"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Sequence number from"
msgstr "Número de secuencia desde"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__show_button_get_status
msgid "Show Button Get Status"
msgstr "Mostrar botón “Obtener estado”"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Signing date:"
msgstr "Fecha de firma:"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_certificate_ids
msgid "Software Certificates"
msgstr "Certificados de software"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_id
msgid "Software ID"
msgstr "ID del software"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_operation_mode
msgid "Software Mode"
msgstr "Modo del software"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_security_code
msgid "Software PIN"
msgstr "NIP del software"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_security_code
msgid ""
"Software PIN created in the DIAN portal to invoice electronically with its "
"own software"
msgstr ""
"NIP del software creado en el portal de la DIAN para facturar "
"electrónicamente con su propio software"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_res_config_settings__l10n_co_dian_operation_mode_ids
msgid "Software configurations of DIAN"
msgstr "Configuraciones de software de la DIAN"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_software_id
msgid ""
"Software identifier provided by the DIAN to invoice electronically with its "
"own software"
msgstr ""
"Identificador de software proporcionado por la DIAN para facturar "
"electrónicamente con su propio software"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Somos Grandes Contribuyentes"
msgstr "Somos Grandes Contribuyentes"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__state
msgid "State"
msgstr "Estado"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Tax regime:"
msgstr "Regimen fiscal:"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "Tax responsability:"
msgstr "Responsabilidad tributaria:"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_account_journal__l10n_co_dian_technical_key
msgid "Technical control key"
msgstr "Clave de control técnico"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__test_environment
msgid "Test Environment"
msgstr "Entorno de prueba"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_company__l10n_co_dian_test_environment
#: model:ir.model.fields,field_description:l10n_co_dian.field_res_config_settings__l10n_co_dian_test_environment
msgid "Test environment"
msgstr "Entorno de prueba"

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_testing_id
msgid "Testing ID"
msgstr "ID de pruebas"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_l10n_co_dian_operation_mode__dian_testing_id
msgid ""
"Testing ID is needed for the certification process with the DIAN and for "
"general testing of electronic invoicing workflows"
msgstr ""
"El ID de pruebas es necesario para el proceso de certificación con la DIAN y"
" para las pruebas generales de los flujos de trabajo de facturación "
"electrónica"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move.py:0
msgid "The %s was accepted by the DIAN."
msgstr "El %s fue aceptado por la DIAN."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_move.py:0
msgid "The %s was validated locally in Demo Mode."
msgstr "El %s fue validado localmente en Modo Demo."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/l10n_co_dian_document.py:0
msgid "The DIAN server did not respond."
msgstr "El servidor de la DIAN no respondió."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/l10n_co_dian_document.py:0
msgid "The DIAN server returned an error (code %s)"
msgstr "El servidor de la DIAN devolvió un error (código %s)"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "The identification number of %s contains '-' but is not a NIT."
msgstr "El número de identificación de %s contiene '-' pero no es un NIT."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "The invoice linked to this credit note has no CUFE."
msgstr "La factura vinculada a esta nota de crédito no tiene CUFE."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid ""
"The issue date can not be older than 5 days or more than 5 days in the "
"future."
msgstr ""
"La fecha de emisión no puede ser anterior a 5 días ni más de 5 días en el "
"futuro."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_journal.py:0
msgid "The journal values are already up to date."
msgstr "Los valores del diario ya están actualizados."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_journal.py:0
msgid "The journal values were successfully updated."
msgstr "Los valores del diario se actualizaron con éxito."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "The original debited invoice has no CUFE."
msgstr "La factura debitada original no tiene CUFE."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/res_config_settings.py:0
msgid ""
"The number of invoices, credit notes, or debit notes to certify must be "
"between 0 and 10."
msgstr ""
"La cantidad de facturas, notas crédito o notas débito a certificar debe "
"estar entre 0 y 10."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "The software PIN is required to compute the CUDE/CUDS."
msgstr "Se requiere el NIP del software para calcular el CUDE/CUDS."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "The support document linked to this credit note has no CUDS."
msgstr "El documento soporte vinculado a esta nota de crédito no tiene CUDS."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "There is no Colombian code on the unit of measure: %s"
msgstr "No hay código de Colombia para la unidad de medida: %s"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid ""
"There is no invoice linked to this credit note but the operation type is "
"'20'."
msgstr ""
"No hay una factura vinculada a esta nota de crédito, pero el tipo de "
"operación es '20'."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "There is no original debited invoice but the operation type is '30'."
msgstr "No hay factura original debitada, pero el tipo de operación es '30'."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid "There is no support document linked to this credit note."
msgstr "No hay documento soporte vinculado a esta nota de crédito."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid ""
"Tick if you are testing workflows for electronic invoicing or if you need to"
" activate the certification process environment"
msgstr ""
"Seleccione la casilla si está probando flujos de trabajo para la facturación"
" electrónica o si necesita activar el entorno de proceso de certificación"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "UBL 2.1"
msgstr "UBL 2.1"

#. module: l10n_co_dian
#: model:ir.model,name:l10n_co_dian.model_account_edi_xml_ubl_dian
msgid "UBL DIAN"
msgstr "UBL DIAN"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/xml_utils.py:0
msgid "URI %(uri)s not found"
msgstr "No se encontró el URI %(uri)s"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "UTF-8"
msgstr "UTF-8"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "Unidad Especial Dirección de Impuestos y Aduanas Nacionales"
msgstr "Unidad Especial - Dirección de Impuestos y Aduanas Nacionales"

#. module: l10n_co_dian
#: model:ir.model.fields,help:l10n_co_dian.field_account_bank_statement_line__l10n_co_edi_cufe_cude_ref
#: model:ir.model.fields,help:l10n_co_dian.field_account_move__l10n_co_edi_cufe_cude_ref
msgid "Unique ID used by the DIAN to identify the invoice."
msgstr ""
"Identificación única recibida por el gobierno cuando se firma la factura."

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_journal.py:0
msgid "Updated the following journal values: %s"
msgstr "Actualizados los siguientes valores del diario: %s"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.view_account_journal_form_inherit_l10n_co_dian
msgid ""
"Using the company VAT and software code, fetch the resolution number, "
"resolution                         date, sequence prefix, sequence numbers "
"and technical control key"
msgstr ""
"Use el NIT de la empresa y el código de software para obtener el número de "
"resolución, la fecha de                         resolución, el prefijo de la"
" secuencia, los números de secuencia y la clave de control técnico"

#. module: l10n_co_dian
#: model:ir.model.constraint,message:l10n_co_dian.constraint_l10n_co_dian_operation_mode_uniq_software_operation_mode
msgid "You cannot have two records with same mode"
msgstr "No puede tener dos registros con el mismo modo"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_vendor_document
msgid "Vendor Bill"
msgstr "Documento Soporte"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_vendor_document
msgid "Vendor Credit Note"
msgstr "Nota de Ajuste del Documento Soporte"

#. module: l10n_co_dian
#. odoo-python
#: code:addons/l10n_co_dian/models/account_edi_xml_ubl_dian.py:0
msgid ""
"Volume in milliliters should be set on the %(field_description)s "
"field for product: %(product_name)s when using IBUA taxes."
msgstr "El volumen en mililitros debe establecerse en el campo del %(field_description)s "
"para el producto: %(product_name)s al usar impuestos IBUA."

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.res_config_settings_view_form
msgid ""
"Warning! This action will perform irreversible changes to your database to "
"best guarantee certification with DIAN. Please make sure you are only "
"running this on a testing database."
msgstr ""
"Atención! Al activar esta acción se realizarán cambios irreversibles a su "
"base de datos con el objetivo de garantizar el éxito de su habilitación con la DIAN. "
"Por favor asegurese de correr la acción en una base de datos de prueba."

#. module: l10n_co_dian
#: model:ir.model.fields,field_description:l10n_co_dian.field_l10n_co_dian_document__zip_key
msgid "Zip Key"
msgstr "Clave del zip"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "dummy"
msgstr "dummy"

#. module: l10n_co_dian
#: model:ir.actions.act_window,name:l10n_co_dian.l10n_co_dian_operation_mode_action
msgid "l10n_co_dian.operation_mode.action"
msgstr "l10n_co_dian.operation_mode.action"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "proforma"
msgstr "proforma"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.attached_document
msgid "text/xml"
msgstr "text/xml"

#. module: l10n_co_dian
#: model_terms:ir.ui.view,arch_db:l10n_co_dian.report_invoice_document
msgid "to"
msgstr "a"

#. module: l10n_co_dian
#: model:mail.template,subject:l10n_co_dian.email_template_edi_credit_note
#: model:mail.template,subject:l10n_co_dian.email_template_edi_invoice
msgid ""
"{{ object.company_id.partner_id._get_vat_without_verification_code() }};{{ "
"object.company_id.name }};{{ object.name }};{{ object.l10n_co_edi_type }};{{"
" object.company_id.partner_id.l10n_co_edi_commercial_name }}"
msgstr ""
"{{ object.company_id.partner_id._get_vat_without_verification_code() }};{{ "
"object.company_id.name }};{{ object.name }};{{ object.l10n_co_edi_type }};{{"
" object.company_id.partner_id.l10n_co_edi_commercial_name }}"
