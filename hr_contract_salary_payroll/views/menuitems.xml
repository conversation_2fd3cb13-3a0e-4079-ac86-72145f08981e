<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <menuitem
        id="menu_hr_payroll_configuration_contract"
        name="Contracts"
        parent="hr_work_entry_enterprise.menu_hr_payroll_configuration"
        sequence="25"/>

    <menuitem
        id="hr_payroll_menu_contract_templates"
        name="Templates"
        action="hr.action_hr_contract_templates"
        parent="menu_hr_payroll_configuration_contract"
        sequence="10"
        groups="hr.group_hr_manager"/>

    <menuitem
        id="hr_payroll_menu_contract_type"
        name="Employment Types"
        action="hr.hr_contract_type_action"
        parent="menu_hr_payroll_configuration_contract"
        sequence="20"
        groups="hr.group_hr_manager"/>

    <menuitem
        id="menu_hr_payroll_salary_calculator"
        name="Salary Calculator"
        action="action_hr_salary_simulator"
        parent="hr_payroll.menu_hr_payroll_employees"
        sequence="40"/>

    <menuitem
        id="hr_contract_salary.menu_salary_package_offer"
        name="Offers"
        parent="hr_payroll.menu_hr_payroll_employees"
        sequence="50"
        groups="base.group_no_one"/>

    <record id="hr_contract_salary.salary_package_menu" model="ir.ui.menu">
        <field name="parent_id" ref="hr_work_entry_enterprise.menu_hr_payroll_configuration"/>
    </record>
</odoo>
