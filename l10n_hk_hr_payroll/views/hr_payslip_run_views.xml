<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_payslip_run_kanban_inherit_l10n_hk_hr_payroll" model="ir.ui.view">
        <field name="name">hr.payslip.run.kanban.inherit.l10n_hk_hr_payroll</field>
        <field name="model">hr.payslip.run</field>
        <field name="inherit_id" ref="hr_payroll.hr_payslip_run_view_kanban"/>
        <field name="arch" type="xml">
            <button name="action_paid" position="before">
                <button name="action_open_hsbc_autopay_wizard" type="object" string="HSBC Autopay Report" class="btn btn-primary"
                        invisible="not l10n_hk_autopay or state not in ['03_close', '04_paid'] or l10n_hk_autopay_export_first_batch or l10n_hk_autopay_export_second_batch" />
            </button>
            <button name="action_paid" position="after">
                <button name="action_open_hsbc_autopay_wizard" type="object" string="HSBC Autopay Report" class="btn btn-secondary"
                        invisible="(not l10n_hk_autopay or state not in ['03_close', '04_paid'] or not l10n_hk_autopay_export_first_batch) and not l10n_hk_autopay_export_second_batch" />
            </button>
            <templates position="before">
                <field name="l10n_hk_autopay" invisible="1" />
                <field name="l10n_hk_autopay_export_first_batch_filename" invisible="1" />
                <field name="l10n_hk_autopay_export_second_batch_filename" invisible="1" />
                <field name="l10n_hk_autopay_export_first_batch_date" invisible="1" />
                <field name="l10n_hk_autopay_export_second_batch_date" invisible="1" />
            </templates>
        </field>
    </record>
</odoo>
