<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="project_enterprise.TaskGanttRenderer.Header" t-inherit="web_gantt.GanttRenderer.Header">
        <xpath expr="//t[@t-foreach='columns']/div" position="inside">
            <t t-set="columnInfo" t-value="columnMilestones[column.index]"/>
            <t t-if="columnInfo.edge &amp;&amp; columnInfo.edge.hasStartDate">
                <div class="o_project_edge_startdate_circle"
                     t-on-mouseenter="(ev) => this.onMilestoneMouseEnter(ev, columnInfo.edge.projects)"
                     t-on-mouseleave="onMilestoneMouseLeave"
                />
            </t>
            <t t-if="columnInfo.hasMilestone">
                <div class="o_project_milestone_diamond"
                    t-att-class="{
                         'o_unreached_milestones': columnInfo.hasDeadLineExceeded,
                         'edge_slot': column_last,
                         'o_project_deadline_milestone': columnInfo.hasDeadline,
                         'o_project_startdate_milestone': !columnInfo.hasDeadline &amp;&amp; columnInfo.hasStartDate,
                     }"
                    t-on-mouseenter="(ev) => this.onMilestoneMouseEnter(ev, columnInfo.projects)"
                    t-on-mouseleave="onMilestoneMouseLeave"
                    >
                    <i class="fa fa-check o_milestones_reached" t-att-class="{ 'edge_slot': column_last }" t-if="columnInfo.allReached"/>
                </div>
            </t>
            <t t-elif="columnInfo.hasDeadline || columnInfo.hasStartDate">
                <div
                    t-att-class="{
                         'o_project_deadline_circle': columnInfo.hasDeadline,
                         'o_project_startdate_circle': !columnInfo.hasDeadline &amp;&amp; columnInfo.hasStartDate,
                     }"
                     t-on-mouseenter="(ev) => this.onMilestoneMouseEnter(ev, columnInfo.projects)"
                     t-on-mouseleave="onMilestoneMouseLeave"
                />
            </t>
        </xpath>
    </t>

    <t t-name="project_enterprise.TaskGanttRenderer.ColoredCellBorder">
        <t t-set="columnInfo" t-value="columnMilestones[column.index]"/>
        <t t-if="columnInfo.edge &amp;&amp; columnInfo.edge.hasStartDate">
            <div class="o_edge_startdate_pin" t-att-style="coloredCellBorderStyle"/>
        </t>
        <t t-if="columnInfo.hasMilestone">
            <div class="o_project_milestone" t-att-style="coloredCellBorderStyle" t-att-class="{ 'o_unreached_milestones': columnInfo.hasDeadLineExceeded }"/>
        </t>
        <t t-elif="columnInfo.hasDeadline">
            <div class="o_project_milestone o_unreached_milestones" t-att-style="coloredCellBorderStyle"/>
        </t>
        <t t-elif="columnInfo.hasStartDate">
            <div class="o_project_milestone o_startdate_pin" t-att-style="coloredCellBorderStyle"/>
        </t>
    </t>

    <t t-name="project_enterprise.TaskGanttRenderer.RowContent" t-inherit="web_gantt.GanttRenderer.RowContent">
        <xpath expr="//div[hasclass('o_gantt_cell')]" position="after">
            <t t-call="project_enterprise.TaskGanttRenderer.ColoredCellBorder">
                <t t-set="coloredCellBorderStyle" t-value="getGridPosition({ column: column.grid.column, row: row.grid.row })"/>
            </t>
        </xpath>
    </t>

     <t t-name="project_enterprise.TaskGanttRenderer.TotalRow" t-inherit="web_gantt.GanttRenderer.TotalRow">
        <xpath expr="//div[hasclass('o_gantt_cell')]" position="after">
            <t t-call="project_enterprise.TaskGanttRenderer.ColoredCellBorder">
                <t t-set="coloredCellBorderStyle" t-value="getGridPosition({ column: column.grid.column })"/>
            </t>
        </xpath>
    </t>

    <t t-name="project_enterprise.TaskGanttRenderer.Pill" t-inherit="web_gantt.GanttRenderer.Pill">
        <xpath expr="//div[hasclass('o_gantt_lock')]" position="before">
            <div t-if="!renderConnectors" class="o_gantt_forbidden fa fa-ban ms-auto me-2" />
        </xpath>
    </t>

</templates>
