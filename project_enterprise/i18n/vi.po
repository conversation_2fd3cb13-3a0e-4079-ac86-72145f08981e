# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_enterprise
#
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"Language: vi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "%s has %s tasks at the same time."
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_kanban_inherited
msgid "<i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Mũi tên\"/>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<span class=\"fst-italic text-muted\"><i class=\"fa fa-lock\"/> Private</span>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Assignees — </strong>"
msgstr "<strong>Người được phân công — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Customer — </strong>"
msgstr "<strong>Khách hàng — </strong>"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Milestone — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
msgid "<strong>Project Manager — </strong>"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "<strong>Project — </strong>"
msgstr "<strong>Dự án — </strong>"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__user_ids
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Assignees"
msgstr "Người được phân công"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Caution: some tasks have not been scheduled"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__create_uid
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__create_date
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Customer"
msgstr "Khách hàng"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Date"
msgstr "Ngày"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__dependency_warning
msgid "Dependency Warning"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_confirm_schedule_wizard_form
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__display_name
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__display_warning_dependency_in_gantt
msgid "Display Warning Dependency In Gantt"
msgstr "Hiển thị phụ thuộc cảnh báo trong Gantt"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__date_end
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_end
#: model:ir.model.fields,field_description:project_enterprise.field_report_project_task_user__planned_date_end
msgid "End date"
msgstr "Ngày kết thúc"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__id
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__id
msgid "ID"
msgstr "ID"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__write_uid
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__write_date
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__line_ids
msgid "Lines"
msgstr "Dòng"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view_no_title
msgid "Milestone"
msgstr "Mốc thời gian"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_mobile
msgid "Mobile"
msgstr "Di động"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/wizard/task_confirm_schedule_wizard.py:0
msgid "No task has been scheduled in the future."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "One parameter is missing to use this method. You should give a start and end dates."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Operation not supported, you should always compare dependency_warning to True or False."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Operation not supported, you should always compare planning_overlap to True or False."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Overlapping Tasks"
msgstr "Công việc chồng chéo"

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_sharing_project_task_view_form_inherited
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form
msgid "Planned Date"
msgstr "Ngày theo Kế hoạch"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_project_view_gantt
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_gantt
msgid "Planning"
msgstr "Kế hoạch"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planning_overlap
msgid "Planning Overlap"
msgstr "Lịch trình trùng lắp"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_project
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_map_view
msgid "Project"
msgstr "Dự án"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_milestone
msgid "Project Milestone"
msgstr "Mốc thời gian dự án"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Save"
msgstr "Lưu"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__schedule_task
msgid "Schedule Task"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_confirm_schedule_wizard_form
msgid "Schedule Tasks"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__selected_line_count
msgid "Selected Line Count"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__show_warnings
msgid "Show Warnings"
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "Some tasks weren't planned because the closest available starting date was too far ahead in the future"
msgstr ""

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__date_begin
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__planned_date_begin
#: model:ir.model.fields,field_description:project_enterprise.field_report_project_task_user__planned_date_begin
msgid "Start date"
msgstr "Ngày bắt đầu"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_street
msgid "Street"
msgstr "Địa chỉ"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__task_id
msgid "Task"
msgstr "Công việc"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_confirm_schedule_line_wizard
msgid "Task confirm schedule line wizard"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_project_task_confirm_schedule_wizard
msgid "Task confirm schedule wizard"
msgstr ""

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Phân tích nhiệm vụ"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.view_task_confirm_schedule_wizard_form
msgid ""
"The following tasks have not been scheduled because the corresponding\n"
"                        employees are unavailable. The next possible dates for these tasks are:"
msgstr ""

#. module: project_enterprise
#: model:ir.model.constraint,message:project_enterprise.constraint_project_task_planned_dates_check
msgid "The planned start date must be before the planned end date."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/wizard/task_confirm_schedule_wizard.py:0
msgid "The task has been successfully scheduled."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/wizard/task_confirm_schedule_wizard.py:0
msgid "The tasks have been successfully scheduled."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This Progress Bar is not implemented."
msgstr "Thanh tiến trình này không được triển khai."

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid ""
"This employee does not have a running contract during the selected period.\n"
"The working hours of the company were used as a reference instead."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This task cannot be planned before Tasks %s, on which it depends."
msgstr ""

#. module: project_enterprise
#. odoo-python
#: code:addons/project_enterprise/models/project_task.py:0
msgid "This user isn't expected to have any tasks assigned during this period because they don't have any running contract. Planned hours :"
msgstr ""

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.res_config_settings_view_form
msgid "Timesheets"
msgstr "Bảng chấm công"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/task_gantt_model.js:0
msgid "Unassigned Tasks"
msgstr "Nhiệm vụ chưa phân công"

#. module: project_enterprise
#: model_terms:ir.ui.view,arch_db:project_enterprise.project_task_view_form_in_gantt
msgid "Unschedule"
msgstr "Không có kế hoạch"

#. module: project_enterprise
#: model:ir.model,name:project_enterprise.model_res_users
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_wizard__user_id
msgid "User"
msgstr "Người dùng"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__user_names
msgid "User Names"
msgstr "Tên người dùng"

#. module: project_enterprise
#. odoo-javascript
#: code:addons/project_enterprise/static/src/task_gantt_renderer.js:0
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__warning
msgid "Warning"
msgstr "Cảnh báo"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task_confirm_schedule_line_wizard__parent_id
msgid "Wizard"
msgstr "Hướng dẫn"

#. module: project_enterprise
#: model:ir.model.fields,field_description:project_enterprise.field_project_task__partner_zip
msgid "Zip"
msgstr "Mã bưu chính"
