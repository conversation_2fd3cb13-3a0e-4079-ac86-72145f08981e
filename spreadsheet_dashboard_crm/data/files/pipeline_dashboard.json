{"version": "18.5.10", "sheets": [{"id": "Sheet1", "name": "Dashboard", "colNumber": 8, "rowNumber": 80, "rows": {"6": {"size": 43}, "7": {"size": 33}, "8": {"size": 27}, "9": {"size": 27}, "10": {"size": 27}, "11": {"size": 27}, "12": {"size": 27}, "13": {"size": 27}, "14": {"size": 27}, "15": {"size": 27}, "16": {"size": 27}, "17": {"size": 27}, "20": {"size": 40}, "21": {"size": 38}, "22": {"size": 25}, "23": {"size": 25}, "24": {"size": 25}, "25": {"size": 25}, "26": {"size": 25}, "27": {"size": 25}, "28": {"size": 25}, "29": {"size": 25}, "30": {"size": 25}, "31": {"size": 25}, "34": {"size": 40}, "35": {"size": 36}, "36": {"size": 27}, "37": {"size": 27}, "38": {"size": 27}, "39": {"size": 27}, "40": {"size": 27}, "41": {"size": 27}, "42": {"size": 27}, "43": {"size": 27}, "44": {"size": 27}, "45": {"size": 27}, "48": {"size": 40}, "49": {"size": 40}, "50": {"size": 27}, "51": {"size": 27}, "52": {"size": 27}, "53": {"size": 27}, "54": {"size": 27}, "55": {"size": 27}, "56": {"size": 27}, "57": {"size": 27}, "58": {"size": 27}, "59": {"size": 27}, "61": {"size": 40}, "62": {"size": 40}, "63": {"size": 27}, "64": {"size": 27}, "65": {"size": 27}, "66": {"size": 27}, "67": {"size": 27}, "68": {"size": 27}, "69": {"size": 27}, "70": {"size": 27}, "71": {"size": 27}, "72": {"size": 27}}, "cols": {"0": {"size": 16}, "1": {"size": 275}, "2": {"size": 100}, "3": {"size": 100}, "4": {"size": 50}, "5": {"size": 275}, "6": {"size": 100}, "7": {"size": 100}}, "merges": [], "cells": {"B7": "[Top Opportunities](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":\"[[\\\"type\\\", \\\"=\\\", \\\"opportunity\\\"]]\",\"context\":{\"group_by\":[]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "B8": "=_t(\"Opportunity\")", "B9": "=ODOO.LIST(1,1,\"name\")", "B10": "=ODOO.LIST(1,2,\"name\")", "B11": "=ODOO.LIST(1,3,\"name\")", "B12": "=ODOO.LIST(1,4,\"name\")", "B13": "=ODOO.LIST(1,5,\"name\")", "B14": "=ODOO.LIST(1,6,\"name\")", "B15": "=ODOO.LIST(1,7,\"name\")", "B16": "=ODOO.LIST(1,8,\"name\")", "B17": "=ODOO.LIST(1,9,\"name\")", "B18": "=ODOO.LIST(1,10,\"name\")", "B21": "[Pipeline Stages](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"type\",\"=\",\"opportunity\"]],\"context\":{\"group_by\":[\"stage_id\",\"team_id\"],\"graph_measure\":\"prorated_revenue\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"stage_id\",\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "B22": "Stage", "B23": "=PIVOT(10,10,false,false,,false)", "B35": "[Top Countries](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"country_id\",\"!=\",false]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "B36": "=_t(\"Country\")", "B37": "=PIVOT.HEADER(5,\"#country_id\",1)", "B38": "=PIVOT.HEADER(5,\"#country_id\",2)", "B39": "=PIVOT.HEADER(5,\"#country_id\",3)", "B40": "=PIVOT.HEADER(5,\"#country_id\",4)", "B41": "=PIVOT.HEADER(5,\"#country_id\",5)", "B42": "=PIVOT.HEADER(5,\"#country_id\",6)", "B43": "=PIVOT.HEADER(5,\"#country_id\",7)", "B44": "=PIVOT.HEADER(5,\"#country_id\",8)", "B45": "=PIVOT.HEADER(5,\"#country_id\",9)", "B46": "=PIVOT.HEADER(5,\"#country_id\",10)", "B49": "[Top Cities](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"city\",\"!=\",false]],\"context\":{\"group_by\":[\"city\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"city\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "B50": "=_t(\"City\")", "B51": "=PIVOT.HEADER(6,\"#city\",1)", "B52": "=PIVOT.HEADER(6,\"#city\",2)", "B53": "=PIVOT.HEADER(6,\"#city\",3)", "B54": "=PIVOT.HEADER(6,\"#city\",4)", "B55": "=PIVOT.HEADER(6,\"#city\",5)", "B56": "=PIVOT.HEADER(6,\"#city\",6)", "B57": "=PIVOT.HEADER(6,\"#city\",7)", "B58": "=PIVOT.HEADER(6,\"#city\",8)", "B59": "=PIVOT.HEADER(6,\"#city\",9)", "B60": "=PIVOT.HEADER(6,\"#city\",10)", "B62": "[Top Salespeople](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"user_id\",\"!=\",false]],\"context\":{\"group_by\":[\"user_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"user_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "B63": "=_t(\"Salesperson\")", "B64": "=PIVOT.HEADER(3,\"#user_id\",1)", "B65": "=PIVOT.HEADER(3,\"#user_id\",2)", "B66": "=PIVOT.HEADER(3,\"#user_id\",3)", "B67": "=PIVOT.HEADER(3,\"#user_id\",4)", "B68": "=PIVOT.HEADER(3,\"#user_id\",5)", "B69": "=PIVOT.HEADER(3,\"#user_id\",6)", "B70": "=PIVOT.HEADER(3,\"#user_id\",7)", "B71": "=PIVOT.HEADER(3,\"#user_id\",8)", "B72": "=PIVOT.HEADER(3,\"#user_id\",9)", "B73": "=PIVOT.HEADER(3,\"#user_id\",10)", "C8": "=_t(\"Stage\")", "C9": "=ODOO.LIST(1,1,\"stage_id\")", "C10": "=ODOO.LIST(1,2,\"stage_id\")", "C11": "=ODOO.LIST(1,3,\"stage_id\")", "C12": "=ODOO.LIST(1,4,\"stage_id\")", "C13": "=ODOO.LIST(1,5,\"stage_id\")", "C14": "=ODOO.LIST(1,6,\"stage_id\")", "C15": "=ODOO.LIST(1,7,\"stage_id\")", "C16": "=ODOO.LIST(1,8,\"stage_id\")", "C17": "=ODOO.LIST(1,9,\"stage_id\")", "C18": "=ODOO.LIST(1,10,\"stage_id\")", "C22": "# Leads", "C36": "=_t(\"# Leads\")", "C37": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",1)", "C38": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",2)", "C39": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",3)", "C40": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",4)", "C41": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",5)", "C42": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",6)", "C43": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",7)", "C44": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",8)", "C45": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",9)", "C46": "=PIVOT.VALUE(5,\"__count\",\"#country_id\",10)", "C50": "=_t(\"# Leads\")", "C51": "=PIVOT.VALUE(6,\"__count\",\"#city\",1)", "C52": "=PIVOT.VALUE(6,\"__count\",\"#city\",2)", "C53": "=PIVOT.VALUE(6,\"__count\",\"#city\",3)", "C54": "=PIVOT.VALUE(6,\"__count\",\"#city\",4)", "C55": "=PIVOT.VALUE(6,\"__count\",\"#city\",5)", "C56": "=PIVOT.VALUE(6,\"__count\",\"#city\",6)", "C57": "=PIVOT.VALUE(6,\"__count\",\"#city\",7)", "C58": "=PIVOT.VALUE(6,\"__count\",\"#city\",8)", "C59": "=PIVOT.VALUE(6,\"__count\",\"#city\",9)", "C60": "=PIVOT.VALUE(6,\"__count\",\"#city\",10)", "C63": "=_t(\"# Leads\")", "C64": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",1)", "C65": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",2)", "C66": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",3)", "C67": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",4)", "C68": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",5)", "C69": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",6)", "C70": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",7)", "C71": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",8)", "C72": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",9)", "C73": "=PIVOT.VALUE(3,\"__count\",\"#user_id\",10)", "D8": "=_t(\"Salesperson\")", "D9": "=ODOO.LIST(1,1,\"user_id\")", "D10": "=ODOO.LIST(1,2,\"user_id\")", "D11": "=ODOO.LIST(1,3,\"user_id\")", "D12": "=ODOO.LIST(1,4,\"user_id\")", "D13": "=ODOO.LIST(1,5,\"user_id\")", "D14": "=ODOO.LIST(1,6,\"user_id\")", "D15": "=ODOO.LIST(1,7,\"user_id\")", "D16": "=ODOO.LIST(1,8,\"user_id\")", "D17": "=ODOO.LIST(1,9,\"user_id\")", "D18": "=ODOO.LIST(1,10,\"user_id\")", "D22": "Revenue", "D36": "=_t(\"Revenue\")", "D37": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",1)", "D38": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",2)", "D39": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",3)", "D40": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",4)", "D41": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",5)", "D42": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",6)", "D43": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",7)", "D44": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",8)", "D45": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",9)", "D46": "=PIVOT.VALUE(5,\"prorated_revenue\",\"#country_id\",10)", "D50": "=_t(\"Revenue\")", "D51": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",1)", "D52": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",2)", "D53": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",3)", "D54": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",4)", "D55": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",5)", "D56": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",6)", "D57": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",7)", "D58": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",8)", "D59": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",9)", "D60": "=PIVOT.VALUE(6,\"prorated_revenue\",\"#city\",10)", "D63": "=_t(\"Revenue\")", "D64": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",1)", "D65": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",2)", "D66": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",3)", "D67": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",4)", "D68": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",5)", "D69": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",6)", "D70": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",7)", "D71": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",8)", "D72": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",9)", "D73": "=PIVOT.VALUE(3,\"prorated_revenue\",\"#user_id\",10)", "F8": "=_t(\"Country\")", "F9": "=ODOO.LIST(1,1,\"country_id\")", "F10": "=ODOO.LIST(1,2,\"country_id\")", "F11": "=ODOO.LIST(1,3,\"country_id\")", "F12": "=ODOO.LIST(1,4,\"country_id\")", "F13": "=ODOO.LIST(1,5,\"country_id\")", "F14": "=ODOO.LIST(1,6,\"country_id\")", "F15": "=ODOO.LIST(1,7,\"country_id\")", "F16": "=ODOO.LIST(1,8,\"country_id\")", "F17": "=ODOO.LIST(1,9,\"country_id\")", "F18": "=ODOO.LIST(1,10,\"country_id\")", "F35": "[Top Sources](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"source_id\",\"!=\",false]],\"context\":{\"group_by\":[\"source_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"source_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "F36": "=_t(\"Source\")", "F37": "=PIVOT.HEADER(8,\"#source_id\",1)", "F38": "=PIVOT.HEADER(8,\"#source_id\",2)", "F39": "=PIVOT.HEADER(8,\"#source_id\",3)", "F40": "=PIVOT.HEADER(8,\"#source_id\",4)", "F41": "=PIVOT.HEADER(8,\"#source_id\",5)", "F42": "=PIVOT.HEADER(8,\"#source_id\",6)", "F43": "=PIVOT.HEADER(8,\"#source_id\",7)", "F44": "=PIVOT.HEADER(8,\"#source_id\",8)", "F45": "=PIVOT.HEADER(8,\"#source_id\",9)", "F46": "=PIVOT.HEADER(8,\"#source_id\",10)", "F49": "[Top Mediums](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"medium_id\",\"!=\",false]],\"context\":{\"group_by\":[\"medium_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"medium_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "F50": "=_t(\"Medium\")", "F51": "=PIVOT.HEADER(7,\"#medium_id\",1)", "F52": "=PIVOT.HEADER(7,\"#medium_id\",2)", "F53": "=PIVOT.HEADER(7,\"#medium_id\",3)", "F54": "=PIVOT.HEADER(7,\"#medium_id\",4)", "F55": "=PIVOT.HEADER(7,\"#medium_id\",5)", "F56": "=PIVOT.HEADER(7,\"#medium_id\",6)", "F57": "=PIVOT.HEADER(7,\"#medium_id\",7)", "F58": "=PIVOT.HEADER(7,\"#medium_id\",8)", "F59": "=PIVOT.HEADER(7,\"#medium_id\",9)", "F60": "=PIVOT.HEADER(7,\"#medium_id\",10)", "F62": "[Top Sales Teams](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"team_id\",\"!=\",false]],\"context\":{\"group_by\":[\"team_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "F63": "=_t(\"Sales Team\")", "F64": "=PIVOT.HEADER(4,\"#team_id\",1)", "F65": "=PIVOT.HEADER(4,\"#team_id\",2)", "F66": "=PIVOT.HEADER(4,\"#team_id\",3)", "F67": "=PIVOT.HEADER(4,\"#team_id\",4)", "F68": "=PIVOT.HEADER(4,\"#team_id\",5)", "F69": "=PIVOT.HEADER(4,\"#team_id\",6)", "F70": "=PIVOT.HEADER(4,\"#team_id\",7)", "F71": "=PIVOT.HEADER(4,\"#team_id\",8)", "F72": "=PIVOT.HEADER(4,\"#team_id\",9)", "F73": "=PIVOT.HEADER(4,\"#team_id\",10)", "G8": "=_t(\"Revenue\")", "G9": "=ODOO.LIST(1,1,\"prorated_revenue\")", "G10": "=ODOO.LIST(1,2,\"prorated_revenue\")", "G11": "=ODOO.LIST(1,3,\"prorated_revenue\")", "G12": "=ODOO.LIST(1,4,\"prorated_revenue\")", "G13": "=ODOO.LIST(1,5,\"prorated_revenue\")", "G14": "=ODOO.LIST(1,6,\"prorated_revenue\")", "G15": "=ODOO.LIST(1,7,\"prorated_revenue\")", "G16": "=ODOO.LIST(1,8,\"prorated_revenue\")", "G17": "=ODOO.LIST(1,9,\"prorated_revenue\")", "G18": "=ODOO.LIST(1,10,\"prorated_revenue\")", "G36": "=_t(\"# Leads\")", "G37": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",1)", "G38": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",2)", "G39": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",3)", "G40": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",4)", "G41": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",5)", "G42": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",6)", "G43": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",7)", "G44": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",8)", "G45": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",9)", "G46": "=PIVOT.VALUE(8,\"__count\",\"#source_id\",10)", "G50": "=_t(\"# Leads\")", "G51": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",1)", "G52": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",2)", "G53": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",3)", "G54": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",4)", "G55": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",5)", "G56": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",6)", "G57": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",7)", "G58": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",8)", "G59": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",9)", "G60": "=PIVOT.VALUE(7,\"__count\",\"#medium_id\",10)", "G63": "=_t(\"# Leads\")", "G64": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",1)", "G65": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",2)", "G66": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",3)", "G67": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",4)", "G68": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",5)", "G69": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",6)", "G70": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",7)", "G71": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",8)", "G72": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",9)", "G73": "=PIVOT.VALUE(4,\"__count\",\"#team_id\",10)", "H8": "=_t(\"Success (%)\")", "H9": "=ODOO.LIST(1,1,\"probability\")", "H10": "=ODOO.LIST(1,2,\"probability\")", "H11": "=ODOO.LIST(1,3,\"probability\")", "H12": "=ODOO.LIST(1,4,\"probability\")", "H13": "=ODOO.LIST(1,5,\"probability\")", "H14": "=ODOO.LIST(1,6,\"probability\")", "H15": "=ODOO.LIST(1,7,\"probability\")", "H16": "=ODOO.LIST(1,8,\"probability\")", "H17": "=ODOO.LIST(1,9,\"probability\")", "H18": "=ODOO.LIST(1,10,\"probability\")", "H36": "=_t(\"Revenue\")", "H37": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",1)", "H38": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",2)", "H39": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",3)", "H40": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",4)", "H41": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",5)", "H42": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",6)", "H43": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",7)", "H44": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",8)", "H45": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",9)", "H46": "=PIVOT.VALUE(8,\"prorated_revenue\",\"#source_id\",10)", "H50": "=_t(\"Revenue\")", "H51": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",1)", "H52": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",2)", "H53": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",3)", "H54": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",4)", "H55": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",5)", "H56": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",6)", "H57": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",7)", "H58": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",8)", "H59": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",9)", "H60": "=PIVOT.VALUE(7,\"prorated_revenue\",\"#medium_id\",10)", "H63": "=_t(\"Revenue\")", "H64": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",1)", "H65": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",2)", "H66": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",3)", "H67": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",4)", "H68": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",5)", "H69": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",6)", "H70": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",7)", "H71": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",8)", "H72": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",9)", "H73": "=PIVOT.VALUE(4,\"prorated_revenue\",\"#team_id\",10)"}, "styles": {"A7:B7": 1, "A21:B21": 1, "A35:B35": 1, "A49:B49": 1, "A62:B62": 1, "F21": 1, "F35": 1, "F49": 1, "F62": 1, "A22:B22": 2, "A36:B36": 2, "A50:B50": 2, "A63:B63": 2, "A8:F8": 2, "F36": 2, "F50": 2, "F63": 2, "A9:B18": 3, "A37:D43": 4, "A51:D60": 4, "A64:D73": 4, "C9:H18": 4, "F37:H46": 4, "F51:H60": 4, "F64:H73": 4, "C22:D22": 5, "C36:D36": 5, "C50:D50": 5, "C63:D63": 5, "G8:H8": 5, "G36:H36": 5, "G50:H50": 5, "G63:H63": 5}, "formats": {}, "borders": {"B9": 1, "B23": 1, "B37": 1, "B51": 1, "B64": 1, "F37": 1, "F51": 1, "F64": 1, "B10:B17": 2, "B24:B31": 2, "B38:B45": 2, "B52:B59": 2, "B65:B72": 2, "F38:F45": 2, "F52:F59": 2, "F65:F72": 2, "B18": 3, "B32": 3, "B46": 3, "B60": 3, "B73": 3, "F46": 3, "F60": 3, "F73": 3, "B49:D49": 4, "B62:D62": 4, "F49:H49": 4, "F62:H62": 4, "B50:D50": 5, "B63:D63": 5, "F50:H50": 5, "F63:H63": 5, "C23": 6, "C37": 6, "C51": 6, "C64": 6, "C9:G9": 6, "G37": 6, "G51": 6, "G64": 6, "C24:C31": 7, "C38:C45": 7, "C52:C59": 7, "C65:C72": 7, "C10:G17": 7, "G38:G45": 7, "G52:G59": 7, "G65:G72": 7, "C32": 8, "C46": 8, "C60": 8, "C73": 8, "C18:G18": 8, "G46": 8, "G60": 8, "G73": 8, "D23": 9, "D37": 9, "D51": 9, "D64": 9, "H9": 9, "H37": 9, "H51": 9, "H64": 9, "D24:D31": 10, "D38:D45": 10, "D52:D59": 10, "D65:D72": 10, "H10:H17": 10, "H38:H45": 10, "H52:H59": 10, "H65:H72": 10, "D32": 11, "D46": 11, "D60": 11, "D73": 11, "H18": 11, "H46": 11, "H60": 11, "H73": 11}, "conditionalFormats": [{"id": "13881108-f614-4278-89c4-a58fda80496c", "rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "G9:G18"}, "ranges": ["B9:B18"]}, {"id": "160a3e6f-6265-4a3e-a84e-a93efce715c6", "rule": {"type": "DataBarRule", "color": 15726335, "rangeValues": "D64:D73"}, "ranges": ["B64:B73"]}, {"id": "adcc4f6a-b796-4ad9-ab48-ecdb91c2ef05", "rule": {"type": "DataBarRule", "color": 16775149, "rangeValues": "H64:H73"}, "ranges": ["F64:F73"]}, {"id": "14099357-8292-4a05-92d3-33656028ed9e", "rule": {"type": "DataBarRule", "color": 16708338, "rangeValues": "D37:D46"}, "ranges": ["B37:B46"]}, {"id": "66f8207d-2f52-4321-a1e2-c8b76117aff0", "rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "D51:D60"}, "ranges": ["B51:B60"]}, {"rule": {"type": "DataBarRule", "color": 15726335, "rangeValues": "H51:H60"}, "id": "0c8a2d29-0965-4f95-a091-6526cd056eea", "ranges": ["F51:F59"]}, {"id": "bd248e0c-82e6-4f75-a8f0-7c25bb0e3ebe", "rule": {"type": "DataBarRule", "color": 16775149, "rangeValues": "H37:H46"}, "ranges": ["F37:F46"]}, {"rule": {"type": "DataBarRule", "color": 16775149, "rangeValues": "D23:D32"}, "id": "df4deb50-e2aa", "ranges": ["B23:B32"]}], "dataValidationRules": [], "figures": [{"id": "09ab3fe3-04d6-4c9f-97ff-bb37fee0e692", "width": 200, "height": 109, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Expected", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!E4", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!D4", "humanize": false, "chartId": "09ab3fe3-04d6-4c9f-97ff-bb37fee0e692"}, "offset": {"x": 0, "y": 9}, "col": 1, "row": 0}, {"id": "5dc98740-8fc9-432d-b386-59e6e5c8b7e8", "width": 200, "height": 109, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Closed", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!E5", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!D5", "humanize": false, "chartId": "5dc98740-8fc9-432d-b386-59e6e5c8b7e8"}, "offset": {"x": 208, "y": 9}, "col": 1, "row": 0}, {"id": "735dabd8-96dc-44a1-9871-f35a94c347f5", "width": 200, "height": 109, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": {"text": "Open opportunities", "color": "#434343", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baselineDescr": {"text": "to close"}, "keyValue": "Data!D7", "humanize": false, "chartId": "735dabd8-96dc-44a1-9871-f35a94c347f5"}, "offset": {"x": 416, "y": 9}, "col": 1, "row": 0}, {"id": "6c739756-da1e-4e9b-bd26-e74e6cd10c88", "width": 490, "height": 340, "tag": "chart", "data": {"title": {"text": "Expected Closing", "bold": true, "fontSize": 20, "color": "#01666B"}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["date_deadline:month", "team_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead", "mode": "bar"}, "searchParams": {"comparison": null, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "groupBy": ["date_deadline:month", "team_id"], "orderBy": []}, "type": "odoo_bar", "dataSets": [{}, {}], "verticalAxisPosition": "left", "stacked": true, "axesDesign": {"x": {"title": {"text": ""}}, "y": {"title": {"text": "Revenue"}}}, "chartId": "6c739756-da1e-4e9b-bd26-e74e6cd10c88", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}}, "offset": {"x": 40, "y": 22}, "col": 4, "row": 19}, {"id": "371be81c-1fac", "col": 0, "row": 6, "offset": {"x": 0, "y": 2}, "width": 1021, "height": 345, "tag": "carousel", "data": {"chartDefinitions": {"2a5063b0-4c4b": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "G8:G18", "yAxisId": "y", "type": "bar"}, {"dataRange": "H8:H18", "type": "line", "yAxisId": "y1"}], "legendPosition": "top", "labelRange": "B8:B18", "title": {"text": "Top Opportunities", "bold": true, "color": "#01666B", "fontSize": 21}, "aggregated": false, "axesDesign": {"y": {"title": {"text": "Revenue"}}, "y1": {"title": {"text": "Probability"}}}}}, "items": [{"type": "chart", "chartId": "2a5063b0-4c4b", "title": "Graph"}, {"type": "carouselDataView", "title": "Top 10"}], "fieldMatching": {}}}, {"id": "d8fb9812-04f0", "col": 0, "row": 19, "offset": {"x": 0, "y": 22}, "width": 491, "height": 340, "tag": "carousel", "data": {"chartDefinitions": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"title": {"text": "Pipeline Stages", "bold": true, "fontSize": 21, "color": "#01666B"}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["stage_id", "team_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead", "mode": "bar"}, "searchParams": {"comparison": null, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "groupBy": ["stage_id", "team_id"], "orderBy": []}, "type": "odoo_bar", "dataSets": [], "verticalAxisPosition": "left", "stacked": true, "axesDesign": {"y": {"title": {"text": "Revenue"}}}}, "a53f3ad6-5a53": {"title": {"text": "Pipeline Stages", "fontSize": 21, "color": "#01666B", "bold": true}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["stage_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead", "mode": "bar", "cumulated": true}, "searchParams": {"context": {"list_view_ref": "crm.crm_case_tree_view_oppor", "default_type": "opportunity"}, "domain": [], "groupBy": ["stage_id"], "orderBy": []}, "type": "odoo_funnel", "actionXmlId": "crm_enterprise.crm_opportunity_action_dashboard", "dataSets": [{}], "cumulative": true}}, "items": [{"type": "chart", "chartId": "a53f3ad6-5a53"}, {"type": "chart", "chartId": "5adf5fa8-e0e4-4e13-a3ac-259d67389cfb", "title": "Bar"}, {"type": "carouselDataView", "title": "Top 10"}], "fieldMatching": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "a53f3ad6-5a53": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date"}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}}}}, {"id": "2e2fe2d8-ad50", "col": 0, "row": 34, "offset": {"x": 0, "y": 0}, "width": 491, "height": 353, "tag": "carousel", "data": {"chartDefinitions": {"af11f776-28fb": {"title": {"text": "Revenues by country", "fontSize": 21, "color": "#01666B", "bold": true}, "background": "#FFFFFF", "legendPosition": "left", "metaData": {"groupBy": ["country_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead", "mode": "bar"}, "searchParams": {"context": {"list_view_ref": "crm.crm_case_tree_view_oppor", "default_type": "opportunity"}, "domain": [], "groupBy": ["country_id"], "orderBy": []}, "type": "odoo_geo", "actionXmlId": "crm_enterprise.crm_opportunity_action_dashboard", "dataSets": [], "colorScale": "blues"}, "df10a4eb-48ac": {"title": {"text": "Leads by country", "color": "#01666B", "fontSize": 21, "bold": true}, "background": "#FFFFFF", "legendPosition": "left", "metaData": {"groupBy": ["country_id"], "measure": "__count", "order": null, "resModel": "crm.lead", "mode": "bar"}, "searchParams": {"context": {"list_view_ref": "crm.crm_case_tree_view_oppor", "default_type": "opportunity"}, "domain": [], "groupBy": ["country_id"], "orderBy": []}, "type": "odoo_geo", "actionXmlId": "crm_enterprise.crm_opportunity_action_dashboard", "dataSets": [], "colorScale": "blues"}}, "items": [{"type": "chart", "chartId": "af11f776-28fb", "title": "Revenue"}, {"type": "chart", "chartId": "df10a4eb-48ac", "title": "Leads"}, {"type": "carouselDataView", "title": "Top 10"}], "fieldMatching": {"af11f776-28fb": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date"}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "df10a4eb-48ac": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date"}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}}}}, {"id": "0f2fc6cd-0b55", "col": 4, "row": 34, "offset": {"x": 35, "y": 0}, "width": 490, "height": 353, "tag": "carousel", "data": {"chartDefinitions": {"6aeb5a7c-00a7": {"title": {"text": "Revenues by source", "fontSize": 21, "color": "#01666B", "bold": true}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["source_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead", "mode": "bar"}, "searchParams": {"context": {"list_view_ref": "crm.crm_case_tree_view_oppor", "default_type": "opportunity"}, "domain": [], "groupBy": ["source_id"], "orderBy": []}, "type": "odoo_treemap", "actionXmlId": "crm_enterprise.crm_opportunity_action_dashboard", "dataSets": []}}, "items": [{"type": "chart", "chartId": "6aeb5a7c-00a7", "title": "Graph"}, {"type": "carouselDataView", "title": "Top 10"}], "fieldMatching": {"6aeb5a7c-00a7": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date"}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}}}}], "tables": [{"range": "B8:H18", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "B23", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 0, "bandedRows": false, "bandedColumns": false, "styleId": "None", "automaticAutofill": false}}, {"range": "B36:D46", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "B50:D60", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "F36:H46", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "F50:H60", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "F63:H73", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "B63:D73", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "a51634aa-7103-45b3-ab61-fd26c0824a1f", "name": "Data", "colNumber": 26, "rowNumber": 102, "rows": {}, "cols": {"0": {"size": 122}, "1": {"size": 141}, "2": {"size": 141}, "3": {"size": 141}, "4": {"size": 141}}, "merges": [], "cells": {"A1": "=_t(\"KPI\")", "A2": "=_t(\"Expected count\")", "A3": "=_t(\"Closed count\")", "A4": "=_t(\"Expected revenue\")", "A5": "=_t(\"Closed revenue\")", "A6": "=_t(\"Percentage closed\")", "A7": "=_t(\"To close\")", "B1": "=_t(\"Current\")", "B2": "=PIVOT.VALUE(1,\"__count\")", "B3": "=PIVOT.VALUE(1,\"__count\",\"won_status\",\"won\")", "B4": "=PIVOT.VALUE(1,\"prorated_revenue\")", "B5": "=PIVOT.VALUE(1,\"prorated_revenue\",\"won_status\",\"won\")", "B6": "=IFERROR(B3/B2)", "B7": "=PIVOT.VALUE(1,\"__count\",\"won_status\",\"pending\")", "C1": "=_t(\"Previous\")", "C2": "=PIVOT.VALUE(2,\"__count\")", "C3": "=PIVOT.VALUE(2,\"__count\",\"won_status\",\"won\")", "C4": "=PIVOT.VALUE(2,\"prorated_revenue\")", "C5": "=PIVOT.VALUE(2,\"prorated_revenue\",\"won_status\",\"won\")", "C6": "=IFERROR(C3/C2)", "C7": "=PIVOT.VALUE(2,\"__count\",\"won_status\",\"pending\")", "D1": "=_t(\"Current\")", "D2": "=FORMAT.LARGE.NUMBER(B2)", "D3": "=FORMAT.LARGE.NUMBER(B3)", "D4": "=FORMAT.LARGE.NUMBER(B4)", "D5": "=FORMAT.LARGE.NUMBER(B5)", "D6": "=B6", "D7": "=FORMAT.LARGE.NUMBER(B7)", "E1": "=_t(\"Previous\")", "E2": "=FORMAT.LARGE.NUMBER(C2)", "E3": "=FORMAT.LARGE.NUMBER(C3)", "E4": "=FORMAT.LARGE.NUMBER(C4)", "E5": "=FORMAT.LARGE.NUMBER(C5)", "E6": "=C6", "E7": "=FORMAT.LARGE.NUMBER(C7)"}, "styles": {"A1:E1": 6, "D2:E7": 7}, "formats": {"B6:E6": 1}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}], "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"fontSize": 11, "bold": true, "textColor": "#434343"}, "3": {"textColor": "#01666B", "verticalAlign": "middle"}, "4": {"textColor": "#434343", "verticalAlign": "middle"}, "5": {"fontSize": 11, "bold": true, "textColor": "#434343", "align": "center"}, "6": {"bold": true}, "7": {"fillColor": "#f8f9fa"}}, "formats": {"1": "0%"}, "borders": {"1": {"right": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "2": {"top": {"style": "medium", "color": "#FFFFFF"}, "right": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "3": {"top": {"style": "medium", "color": "#FFFFFF"}, "right": {"style": "medium", "color": "#FFFFFF"}}, "4": {"bottom": {"style": "thin", "color": "#CCCCCC"}}, "5": {"top": {"style": "thin", "color": "#CCCCCC"}}, "6": {"left": {"style": "medium", "color": "#FFFFFF"}, "right": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "7": {"left": {"style": "medium", "color": "#FFFFFF"}, "top": {"style": "medium", "color": "#FFFFFF"}, "right": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "8": {"left": {"style": "medium", "color": "#FFFFFF"}, "top": {"style": "medium", "color": "#FFFFFF"}, "right": {"style": "medium", "color": "#FFFFFF"}}, "9": {"left": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "10": {"left": {"style": "medium", "color": "#FFFFFF"}, "top": {"style": "medium", "color": "#FFFFFF"}, "bottom": {"style": "medium", "color": "#FFFFFF"}}, "11": {"left": {"style": "medium", "color": "#FFFFFF"}, "top": {"style": "medium", "color": "#FFFFFF"}}}, "revisionId": "START_REVISION", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"1": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "id": "1", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "expected - current", "sortedColumn": null, "formulaId": "1", "columns": [], "rows": [{"fieldName": "won_status"}]}, "2": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": -1}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "id": "2", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "expected - previous", "sortedColumn": null, "formulaId": "2", "columns": [], "rows": [{"fieldName": "won_status"}]}, "3": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["user_id", "!=", false]], "id": "3", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top salespeople", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "3", "columns": [], "rows": [{"fieldName": "user_id"}]}, "4": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["team_id", "!=", false]], "id": "4", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top sales team", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "4", "columns": [], "rows": [{"fieldName": "team_id"}]}, "5": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["country_id", "!=", false]], "id": "5", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top countries", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "5", "columns": [], "rows": [{"fieldName": "country_id"}]}, "6": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["city", "!=", false]], "id": "6", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top cities", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "6", "columns": [], "rows": [{"fieldName": "city"}]}, "7": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["medium_id", "!=", false]], "id": "7", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top mediums", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "7", "columns": [], "rows": [{"fieldName": "medium_id"}]}, "8": {"type": "ODOO", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date", "offset": 0}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["source_id", "!=", false]], "id": "8", "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue", "fieldName": "prorated_revenue"}], "model": "crm.lead", "name": "top sources", "sortedColumn": {"measure": "prorated_revenue", "order": "desc", "domain": []}, "formulaId": "8", "columns": [], "rows": [{"fieldName": "source_id"}]}, "d2a6f75c-a0a1": {"type": "ODOO", "domain": [], "context": {"list_view_ref": "crm.crm_case_tree_view_oppor", "default_type": "opportunity"}, "sortedColumn": {"domain": [], "order": "desc", "measure": "prorated_revenue:sum"}, "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "prorated_revenue:sum", "fieldName": "prorated_revenue", "aggregator": "sum"}], "model": "crm.lead", "columns": [], "rows": [{"fieldName": "stage_id"}], "name": "Pipeline Analysis by Stage (copy)", "actionXmlId": "crm_enterprise.crm_opportunity_action_dashboard", "formulaId": "10", "fieldMatching": {"bf6dcdb0-d22d-468c-b6a9-64f717005043": {"chain": "date_deadline", "type": "date"}, "9f3d4602-0b4b-4eb4-b013-959d72af88ab": {"chain": "stage_id", "type": "many2one"}, "9c8a3e99-903a-40a3-8799-5eaf098b884f": {"chain": "user_id", "type": "many2one"}, "aaba8a27-cf83-4397-a9ad-a85bba4c8016": {"chain": "team_id", "type": "many2one"}, "f6ef716d-3135-4963-b645-bc12c6a3421b": {"chain": "country_id", "type": "many2one"}, "ec772492-b1db-4a63-be55-a4e0c3167edf": {"chain": "city", "type": "char"}, "c21a4660-9a0a-4757-8d28-1c8d95f73edb": {"chain": "medium_id", "type": "many2one"}, "c33d6d0a-df2d-4150-86b0-112cd250ddbd": {"chain": "source_id", "type": "many2one"}}}}, "pivotNextId": 12, "customTableStyles": {}, "globalFilters": [{"id": "bf6dcdb0-d22d-468c-b6a9-64f717005043", "type": "date", "label": "Period", "defaultValue": {}}, {"id": "9f3d4602-0b4b-4eb4-b013-959d72af88ab", "type": "relation", "label": "Stage", "modelName": "crm.stage", "defaultValueDisplayNames": []}, {"id": "9c8a3e99-903a-40a3-8799-5eaf098b884f", "type": "relation", "label": "Salesperson", "modelName": "res.users", "defaultValueDisplayNames": []}, {"id": "aaba8a27-cf83-4397-a9ad-a85bba4c8016", "type": "relation", "label": "Sales Team", "modelName": "crm.team", "defaultValueDisplayNames": []}, {"id": "f6ef716d-3135-4963-b645-bc12c6a3421b", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValueDisplayNames": []}, {"id": "ec772492-b1db-4a63-be55-a4e0c3167edf", "type": "text", "label": "City"}, {"id": "c21a4660-9a0a-4757-8d28-1c8d95f73edb", "type": "relation", "label": "Medium", "modelName": "utm.medium", "defaultValueDisplayNames": []}, {"id": "c33d6d0a-df2d-4150-86b0-112cd250ddbd", "type": "relation", "label": "Source", "modelName": "utm.source", "defaultValueDisplayNames": []}], "lists": {"1": {"columns": ["name", "email_from", "phone", "country_id", "user_id", "activity_ids", "my_activity_date_deadline", "prorated_revenue", "stage_id", "probability"], "domain": [["type", "=", "opportunity"]], "model": "crm.lead", "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "orderBy": [{"name": "prorated_revenue", "asc": false}], "id": "1", "name": "list opps", "fieldMatching": {}}}, "listNextId": 2, "chartOdooMenusReferences": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": "crm.crm_menu_root", "6c739756-da1e-4e9b-bd26-e74e6cd10c88": "crm.crm_menu_root", "09ab3fe3-04d6-4c9f-97ff-bb37fee0e692": "crm.crm_opportunity_report_menu", "5dc98740-8fc9-432d-b386-59e6e5c8b7e8": "crm.crm_opportunity_report_menu", "735dabd8-96dc-44a1-9871-f35a94c347f5": "crm.menu_crm_opportunities", "2a5063b0-4c4b": "crm.crm_opportunity_report_menu", "a53f3ad6-5a53": "crm.crm_opportunity_report_menu", "df10a4eb-48ac": "crm.crm_opportunity_report_menu", "6aeb5a7c-00a7": "crm.crm_opportunity_report_menu", "af11f776-28fb": "crm.crm_opportunity_report_menu"}}