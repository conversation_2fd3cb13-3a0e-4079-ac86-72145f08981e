<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <record id="account_auto_transfer_rule" model="ir.rule">
        <field name="name">Account Automatic Transfer</field>
        <field name="model_id" ref="model_account_transfer_model"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>
</data>
</odoo>
