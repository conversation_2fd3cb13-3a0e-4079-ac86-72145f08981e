# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span> til </span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "Konto overførsel model"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "Konto overførsel model linje"

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
msgid "Account automatic transfers: Perform transfers"
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "Aktivér"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Adds a condition to only transfer the sum of the lines from the origin accounts that match these analytic accounts to the destination account"
msgstr "Tilføjer en betingelse til kun at overføre summen af linjerne fra oprindelseskonti som stemmer overens med disse analytiske konti, til destinations kontoen"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Adds a condition to only transfer the sum of the lines from the origin accounts that match these partners to the destination account"
msgstr "Tilføj en betingelse til kun at overføre summen af linjerne fra oprindelseskonti der stemmer overens med disse partnere, til destinations kontoen"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "Aanlytisk filter"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "Automatiseret overførsel"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (%s%% from account %s)"
msgstr "Automatiseret overførsel (%s %% fra konto %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (-%s%%)"
msgstr "Automatisk overførsel (-%s%%)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with analytic account(s): %s and partner(s): %s)"
msgstr "Automatisk Overførsel (posteringer med analytisk konti: %s og partner(e): %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "Automatisk overførsel (posteringer med analytisk konti: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "Automatisk Overførsel (posteringer med partner(e): %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (from account %s with analytic account(s): %s and partner(s): %s)"
msgstr "Automatisk overførsel (fra konto %s med analytisk konti: %s og partner(e): %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (from account %s with analytic account(s): %s)"
msgstr "Automatisk overførsel (fra konto %s med analytisk konti: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (from account %s with partner(s): %s)"
msgstr "Automatisk Overførsel (fra konto %s med partner(e): %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (to account %s)"
msgstr "Automatisk overførsel (til konto %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
msgid "Automatic Transfers"
msgstr "Automatiske overførsler"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "Virksomhed knyttet til denne konto"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "Udregn overførsel"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "Beskrivelse"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "Destinations konto"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "Destinationskonti"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "Destinations journal"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "Deaktivér"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "Deaktiveret"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "Hyppighed"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "Genererede posteringer"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "Genererede bevægelser"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "ID"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS Automatic Transfers"
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "Journal"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "Postering"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "Månedlig"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "Monthly IFRS rent expense transfer"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "Bevæg ID antal"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "Bevæg model"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "Bevæg modeller"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "Navn"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "Kun en konto per overførsel model"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "Oprindelseskonti"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_payment__transfer_model_id
msgid "Originating Model"
msgstr "Oprindelig model"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "Partner filter"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "Procent"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "Procent (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "Procent er skrivebeskyttet"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percentage of the sum of lines from the origin accounts will be transferred to the destination account"
msgstr "Procent af summen af linjer fra oprindelseskonti vil blive overført til destinationskontoen"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "Periode"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "Kvartalvis"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "Kører"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "Start dato"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "Stat"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "Stop dato"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The analytic filter %s is duplicated"
msgstr "Det analytiske filter %s er duplikeret"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The partner filter %s in combination with the analytic filter %s is duplicated"
msgstr "Partner filtret %s i kombination med det analytiske filter %s er duplikeret"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The partner filter %s is duplicated"
msgstr "Partnerfiltret %s er duplikeret"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The total percentage (%s) should be less or equal to 100!"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "Samlet procent"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "Overførsel model"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "Overførsler"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "Årligt"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "Yearly liabilites auto transfers"
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr ""
