<odoo>
    <record id="hr_contract_template_view_form" model="ir.ui.view">
        <field name="name">hr.contract.template.view.form.inherit.l10_jo_hr_payroll</field>
        <field name="model">hr.version</field>
        <field name="inherit_id" ref="hr.hr_contract_template_form_view"/>
        <field name="arch" type="xml">
            <group name="salary_left" position="inside">
                <label for="l10n_jo_tax_exemption" invisible="country_code != 'JO'" string="Tax Exemption"/>
                <div class="o_row" name="l10n_jo_tax_exemption" invisible="country_code != 'JO'">
                    <field name="l10n_jo_tax_exemption" class="o_hr_narrow_field"/>
                    <span>/ year</span>
                </div>
                <label for="l10n_jo_housing_allowance" invisible="country_code != 'JO'" string="Housing Allowance"/>
                <div class="o_row" name="l10n_jo_housing_allowance" invisible="country_code != 'JO'">
                    <field name="l10n_jo_housing_allowance" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
                <label for="l10n_jo_transportation_allowance" invisible="country_code != 'JO'" string="Transportation Allowances"/>
                <div class="o_row" name="l10n_jo_transportation_allowance" invisible="country_code != 'JO'">
                    <field name="l10n_jo_transportation_allowance" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
                <label for="l10n_jo_other_allowances" invisible="country_code != 'JO'" string="Other Allowances"/>
                <div class="o_row" name="l10n_jo_other_allowances" invisible="country_code != 'JO'">
                    <field name="l10n_jo_other_allowances" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
            </group>

        </field>
    </record>
</odoo>
