<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-inherit="website_sale_stock.product_availability" t-inherit-mode="extension">
        <xpath expr="//div[@id='out_of_stock_message']" position="attributes">
            <attribute name="t-if">!is_rental</attribute>
        </xpath>
        <div id="threshold_message" position="attributes">
            <attribute name="t-elif" add="!is_rental" separator=" and "/>
        </div>
        <xpath expr="//div[@id='threshold_message']" position="after">
            <div id="threshold_message_renting"
                 t-if="!(free_qty lte 0 and !cart_qty) and show_availability and free_qty lte available_threshold and is_rental"
                 t-attf-class="availability_message_#{product_template} text-warning fw-bold">
                Only <t t-out='free_qty'/> <t t-out="uom_name" /> still available during the selected period.
            </div>
        </xpath>
        <xpath expr="//span[@id='already_in_cart_message']" position="attributes">
            <attribute name="t-if">!allow_out_of_stock_order and show_availability and cart_qty and !is_rental</attribute>
        </xpath>
        <xpath expr="//span[@id='already_in_cart_message']" position="after">
            <div id="already_in_cart_message_rental" t-if="!allow_out_of_stock_order and show_availability and cart_qty and is_rental"
                 t-attf-class="availability_message_#{product_template} text-warning mt8">
                <t t-if='!free_qty'>
                    You already added all the available product in your cart during the selected period.
                </t>
                <t t-else=''>
                    You already added <t t-out="cart_qty" /> <t t-out="uom_name" /> in your cart for the selected period.
                </t>
            </div>
        </xpath>
    </t>
</templates>
