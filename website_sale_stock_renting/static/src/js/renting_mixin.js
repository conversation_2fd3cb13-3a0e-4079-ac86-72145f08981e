import { formatDate, formatDateTime } from "@web/core/l10n/dates";
import { _t } from "@web/core/l10n/translation";
import { RentingMixin } from '@website_sale_renting/js/renting_mixin';

const oldGetInvalidMessage = RentingMixin._getInvalidMessage;
/**
 * Override to take the stock renting availabilities into account.
 *
 * @override
 */
RentingMixin._getInvalidMessage = function (startDate, endDate, productId) {
    // Create defensive copies to prevent mutations from affecting our logic
    const originalStartDate = startDate;
    const originalEndDate = endDate;

    let message = oldGetInvalidMessage.apply(this, arguments);
    if (message || !originalStartDate || !originalEndDate || !this.rentingAvailabilities || this.preparationTime === undefined) {
        return message;
    }
    if (originalStartDate < luxon.DateTime.now().plus({hours: this.preparationTime})) {
        return _t("Your rental product cannot be prepared as fast, please rent later.");
    }
    if (!this.rentingAvailabilities[productId]) {
        return message;
    }
    let end = luxon.DateTime.now();
    for (const interval of this.rentingAvailabilities[productId]) {
        if (interval.start < originalEndDate) {
            end = interval.end;
            if (this._isDurationWithHours()) {
                end = end.plus({hours: this.preparationTime});
            }
            if (end > originalStartDate) {
                if (interval.quantity_available <= 0) {
                    if (!message) {
                        message = _t("The product is not available for the following time period(s):\n");
                    }
                    message +=
                        " " +
                        _t("- From %(startPeriod)s to %(endPeriod)s.\n", {
                            startPeriod: this._isDurationWithHours()
                                ? formatDateTime(interval.start)
                                : formatDate(interval.start),
                            endPeriod: this._isDurationWithHours()
                                ? formatDateTime(end)
                                : formatDate(end),
                        });
                }
            }
            end -= interval.end;
        } else {
            break;
        }
    }
    return message;
};
