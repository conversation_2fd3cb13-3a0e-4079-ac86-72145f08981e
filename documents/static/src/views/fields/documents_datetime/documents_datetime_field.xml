<templates>
    <t t-name="documents.DocumentsDatetimeField">
        <div class="d-flex align-items-center">
            <div t-att-title="props.label" class="text-primary">Exp: <span t-out="formattedLocalExpirationDate"/></div>
            <button t-on-click="onRemove" class="btn btn-link ms-2 p-0" title="Clear" t-if="!props.readonly">
                <i class="fa fa-fw fa-times"/>
            </button>
            <DocumentsDatetimeBtnField name="this.props.name" record="this.props.record" label.translate="Edit"
                btnClasses="'btn-link p-0'" icon="'edit'" t-if="!props.readonly"/>
        </div>
    </t>
</templates>
