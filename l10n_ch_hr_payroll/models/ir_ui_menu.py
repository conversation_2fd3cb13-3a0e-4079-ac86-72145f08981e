# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models


class IrUiMenu(models.Model):
    _inherit = 'ir.ui.menu'

    def _load_menus_blacklist(self):
        res = super()._load_menus_blacklist()
        user_swiss_companies = self.env.user.company_ids.filtered(lambda c: c.country_code == 'CH')
        if user_swiss_companies:
            res.append(self.env.ref('hr_work_entry_enterprise.menu_hr_payroll_work_entries_root').id)
            res.append(self.env.ref('hr_payroll.hr_menu_salary_attachments').id)
            res.append(self.env.ref('hr_payroll.menu_hr_work_entry_report').id)
            res.append(self.env.ref('hr_work_entry_enterprise.menu_hr_work_entry_type_view').id)
        return res
