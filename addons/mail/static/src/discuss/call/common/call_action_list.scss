.o-discuss-CallActionList-bar button:not(.btn-danger):not(.btn-success), .o-discuss-CallActionList-dropdown {
    background-color: var(--o-discuss-CallActionList-bgColor, #{$o-gray-800});
    color: #FFFFFF;
}

.o-discuss-CallActionList-bar button {
    border-color: rgba(0, 0, 0, .5) !important;

    &:active {
        transform: scale(0.90);
    }
    &.rounded-circle {
        aspect-ratio: 1;
    }
}

.o-discuss-CallActionList-pulse {
    animation: pulse 1s infinite;
}

.o-discuss-CallActionList-dropdown {
    background-color: $o-mail-CallMenu-bgColor;
    border-color: $o-mail-CallMenu-bgColor-lighter;

    .o-discuss-CallActionList-dropdownItem {
        &:hover {
            background-color: $o-mail-CallMenu-bgColor-lighter;
        }
        &:active {
            transform: scale(0.95);
        }
    }
}
