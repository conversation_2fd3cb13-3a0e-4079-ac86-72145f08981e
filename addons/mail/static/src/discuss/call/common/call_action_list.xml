<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="discuss.CallActionList">
        <div class="o-discuss-CallActionList d-flex flex-column justify-content-center" t-attf-class="{{ className }}" t-ref="root">
            <div class="o-discuss-CallActionList-bar d-flex align-items-center flex-wrap justify-content-between" t-att-class="{ 'w-100 ps-2 pe-2': isSmall }">
                <t t-if="isOfActiveCall and rtc.selfSession">
                    <t t-foreach="callActions.actions.slice(0, 4)" t-as="action" t-key="action_index">
                        <CallActionButton action="action" isSmall="isSmall" isActive="action.isActive"/>
                    </t>
                    <CallPopover position="'top-end'" clickToClose="true" contentClass="'o-discuss-CallActionList-dropdown p-1'">
                        <button class="btn smaller d-flex m-1 border rounded-circle opacity-100 opacity-75-hover align-items-center p-0" t-att-title="MORE" t-ref="more" t-on-mouseenter="onMouseenterMore" t-on-mouseleave="onMouseleaveMore" t-on-click="onClickMore" t-att-class="{
                            'o-discuss-CallActionList-pulse': isPromotingFullscreen,
                            'shadow': isPromotingFullscreen
                         }">
                            <i class="oi oi-ellipsis-v m-2 oi-fw" t-att-class="{ 'oi-large': !isSmall }"/>
                        </button>
                        <t t-set-slot="content">
                            <div class="d-flex flex-column py-0">
                                <span t-foreach="callActions.actions.slice(4)" t-as="action" t-key="action_index" class="o-discuss-CallActionList-dropdownItem cursor-pointer rounded-1 d-flex align-items-center px-2 py-2 m-0" t-att-title="action.name" t-on-click="action.select">
                                    <i t-att-class="{
                                        'fa fa-fw': (action.isActive or !action.inactiveIcon ? action.icon : action.inactiveIcon).includes('fa-'), 
                                        'oi oi-fw': (action.isActive or !action.inactiveIcon ? action.icon : action.inactiveIcon).includes('oi-'),
                                        [action.inactiveIcon]: !action.isActive,
                                        [action.icon]: action.isActive or !action.inactiveIcon,
                                    }"/>
                                    <span class="mx-2" t-out="action.name"/>
                                </span>
                            </div>
                        </t>
                    </CallPopover>
                </t>
                <button t-if="props.thread.selfMember?.rtc_inviting_session_id?.is_camera_on" class="btn smaller btn-success d-flex m-1 border-0 rounded-circle shadow-none align-items-center p-0"
                    aria-label="Accept with camera"
                    title="Accept with camera"
                    t-att-disabled="rtc.state.hasPendingRequest"
                    t-on-click="(ev) => this.onClickToggleAudioCall(ev, { camera: true })">
                    <i class="fa fa-video-camera fa-fw m-2" t-att-class="{ 'fa-lg': !isSmall }"/>
                </button>
                <t t-if="!isOfActiveCall and props.thread.useCameraByDefault !== null">
                    <button t-if="props.thread.selfMember?.rtc_inviting_session_id"
                        class="btn btn-danger smaller d-flex gap-1 m-1 border-0 align-items-center rounded-3"
                        t-att-disabled="rtc.state.hasPendingRequest"
                        aria-label="Reject"
                        title="Reject"
                        t-on-click="onClickRejectCall">
                        Reject
                    </button>
                    <t t-set="joinBackText" t-if="props.thread.useCameraByDefault">Join Video Call</t>
                    <t t-else="" t-set="joinBackText">Join Call</t>
                    <button
                        class="btn btn-success smaller d-flex gap-1 m-1 border-0 align-items-center rounded-3"
                        t-att-disabled="rtc.state.hasPendingRequest"
                        t-att-aria-label="joinBackText"
                        t-att-title="joinBackText"
                        t-on-click="(ev) => this.onClickToggleAudioCall(ev, { camera: props.thread.useCameraByDefault })"
                    >
                        <i class="fa fa-fw" t-att-class="{'fa-lg': !isSmall, 'fa-video-camera': props.thread.useCameraByDefault, 'fa-phone': !props.thread.useCameraByDefault}"/>Join
                    </button>
                </t>
                <t t-else="">
                    <button t-if="props.thread.selfMember?.rtc_inviting_session_id and !isOfActiveCall" class="btn smaller btn-danger d-flex m-1 border-0 rounded-circle shadow-none align-items-center p-0"
                        aria-label="Reject"
                        title="Reject"
                        t-att-disabled="rtc.state.hasPendingRequest"
                        t-on-click="onClickRejectCall">
                        <i class="oi oi-close fa-fw m-2" t-att-class="{ 'fa-lg': !isSmall }"/>
                    </button>
                    <button
                        t-if="!isOfActiveCall"
                        class="btn btn-success smaller d-flex m-1 border-0 rounded-circle shadow-none align-items-center p-0"
                        aria-label="Join Video Call"
                        title="Join Video Call"
                        t-att-disabled="rtc.state.hasPendingRequest"
                        t-on-click="(ev) => this.onClickToggleAudioCall(ev, { camera: true })"
                    >
                        <i class="fa fa-fw fa-video-camera m-2" t-att-class="{ 'fa-lg': !isSmall }"/>
                    </button>
                    <t t-if="isOfActiveCall" t-set="callText">Disconnect</t>
                    <t t-else="" t-set="callText">Join Call</t>
                    <button class="btn smaller d-flex m-1 border-0 rounded-circle shadow-none align-items-center p-0"
                        t-att-aria-label="callText"
                        t-att-class="{ 'btn-danger': isOfActiveCall, 'btn-success': !isOfActiveCall }"
                        t-att-disabled="rtc.state.hasPendingRequest"
                        t-att-title="callText"
                        t-on-click="onClickToggleAudioCall">
                        <i class="fa fa-phone fa-fw m-2" t-att-class="{ 'fa-lg': !isSmall }"/>
                    </button>
                </t>
            </div>
            <div t-if="isMobileOS and store.settings.use_push_to_talk and isOfActiveCall" class="d-flex align-items-center flex-wrap justify-content-between p-2">
                <button class="o-discuss-CallActionList-pushToTalk btn btn-primary d-flex w-100 border-0 shadow-none"
                    aria-label="Push to talk"
                    t-on-touchstart.stop="rtc.onPushToTalk"
                    t-on-touchend.stop="rtc.setPttReleaseTimeout">
                    <span class="w-100 fs-4 text-center">Push to talk</span>
                </button>
            </div>
        </div>
    </t>

</templates>
