<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <t t-name="hr_skills.SkillsListRenderer" t-inherit-mode="primary" t-inherit="web.ListRenderer">
        <xpath expr="//table" position="attributes">
            <attribute name="t-attf-class" add="mb-1 {{ !isEditable ? 'cursor-default' : '' }} {{ !showTable ? 'd-none' : ''}} o_skill_table" separator=" "/>
        </xpath>
        <xpath expr="//thead" position="attributes">
            <attribute name="style">visibility: collapse;</attribute>
        </xpath>
        <xpath expr="//table" position="before">
            <div name="skills_header" class="text-uppercase fw-bolder small" style="margin-top: 2rem; box-shadow: 0 1px 0 #e6e6e6">
                Skills
                <a t-if="!isEmpty &amp;&amp; showTimeline" t-on-click="openSkillsReport" class="float-end cursor-pointer">
                    <span class="fa fa-line-chart me-1"/>
                    Timeline
                </a>
            </div>
        </xpath>
        <xpath expr="//table" position="after">
            <t t-if="!showTable">
                <t t-if="!anySkills">
                    <div name="no_skills" class="ms-4 mt-4 text-muted fst-italic oe_inline">
                        <p>
                            There are no skills defined in the library.
                        </p>
                        <button t-on-click="skillTypesAction"
                            class="btn btn-secondary mt-3 text-center"
                            role="button" t-if="isEditable">
                            Create Skills
                        </button>
                    </div>
                </t>
                <t t-else="">
                    <div name="skills_available" class="ms-4 mt-4 text-muted fst-italic oe_inline">
                        <p>You can add skills from our library to the employee profile.<br/>
                            If skills are missing, they can be created by an HR officer.
                        </p>
                        <button t-on-click="props.onAdd"
                            class="btn btn-secondary mt-3 text-center"
                            role="button" t-if="isEditable">
                            Pick a skill from the list
                        </button>
                    </div>
                </t>
            </t>
        </xpath>
    </t>

    <t t-name="hr_skills.SkillsListRenderer.Rows">
        <t t-foreach="Object.entries(groupedList)" t-as="skill_group" t-key="skill_group[0]">
            <tr class="o_group_has_content o_group_header">
                <th tabindex="-1" class="o_group_name" t-att-colspan="colspan">
                    <div class="d-flex justify-content-between align-items-center">
                        <span t-esc="skill_group[1].name"/>
                        <button class="btn btn-secondary btn-sm"
                            t-if="isEditable"
                            t-on-click="() => props.onAdd({ context: { default_skill_type_id: skill_group[1].id }})"
                            role="button">ADD</button>
                    </div>
                </th>
            </tr>
            <t t-foreach="skill_group[1].list.records" t-as="record" t-key="record.id">
                <t t-set="group" t-value="skill_group[1]"/>
                <t t-call="{{ constructor.recordRowTemplate }}"/>
            </t>
        </t>
    </t>
</odoo>
