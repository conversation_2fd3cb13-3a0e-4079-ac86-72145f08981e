
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'Website',
    'category': 'Website/Website',
    'sequence': 20,
    'summary': 'Enterprise website builder',
    'website': 'https://www.odoo.com/app/website',
    'version': '1.0',
    'depends': [
        'digest',
        'web',
        'web_editor',
        'html_editor',
        'http_routing',
        'portal',
        'social_media',
        'auth_signup',
        'mail',
        'google_recaptcha',
        'utm',
        'html_builder',
    ],
    'external_dependencies': {
        'python': ['geoip2'],
        'apt': {
            'geoip2': 'python3-geoip2',
        },
    },
    'installable': True,
    'data': [
        # security.xml first, data.xml need the group to exist (checking it)
        'security/website_security.xml',
        'security/ir.model.access.csv',
        'data/image_library.xml',
        'data/ir_asset.xml',
        'data/ir_cron_data.xml',
        'data/mail_mail_data.xml',
        'data/website_data.xml',
        'data/website_visitor_cron.xml',
        'data/digest_data.xml',
        'views/website_technical_views.xml',
        'views/website_templates.xml',
        'views/snippets/snippets.xml',
        'views/snippets/s_framed_intro.xml',
        'views/snippets/s_title.xml',
        'views/snippets/s_cover.xml',
        'views/snippets/s_text_cover.xml',
        'views/snippets/s_image_text_box.xml',
        'views/snippets/s_striped_top.xml',
        'views/snippets/s_text_image.xml',
        'views/snippets/s_image_text.xml',
        'views/snippets/s_mockup_image.xml',
        'views/snippets/s_instagram_page.xml',
        'views/snippets/s_image_text_overlap.xml',
        'views/snippets/s_banner.xml',
        'views/snippets/s_bento_block.xml',
        'views/snippets/s_snippet_group.xml',
        'views/snippets/s_text_block.xml',
        'views/snippets/s_features.xml',
        'views/snippets/s_features_wall.xml',
        'views/snippets/s_three_columns.xml',
        'views/snippets/s_key_benefits.xml',
        'views/snippets/s_picture.xml',
        'views/snippets/s_image_punchy.xml',
        'views/snippets/s_bento_grid.xml',
        'views/snippets/s_carousel.xml',
        'views/snippets/s_carousel_intro.xml',
        'views/snippets/s_carousel_cards.xml',
        'views/snippets/s_alert.xml',
        'views/snippets/s_motto.xml',
        'views/snippets/s_card.xml',
        'views/snippets/s_cards_grid.xml',
        'views/snippets/s_cards_soft.xml',
        'views/snippets/s_share.xml',
        'views/snippets/s_social_media.xml',
        'views/snippets/s_rating.xml',
        'views/snippets/s_hr.xml',
        'views/snippets/s_closer_look.xml',
        'views/snippets/s_facebook_page.xml',
        'views/snippets/s_image_gallery.xml',
        'views/snippets/s_countdown.xml',
        'views/snippets/s_product_catalog.xml',
        'views/snippets/s_comparisons.xml',
        'views/snippets/s_comparisons_horizontal.xml',
        'views/snippets/s_company_team.xml',
        'views/snippets/s_company_team_basic.xml',
        'views/snippets/s_company_team_shapes.xml',
        'views/snippets/s_company_team_detail.xml',
        'views/snippets/s_company_team_spotlight.xml',
        'views/snippets/s_company_team_grid.xml',
        'views/snippets/s_company_team_card.xml',
        'views/snippets/s_call_to_action.xml',
        'views/snippets/s_references.xml',
        'views/snippets/s_references_social.xml',
        'views/snippets/s_references_grid.xml',
        'views/snippets/s_popup.xml',
        'views/snippets/s_faq_list.xml',
        'views/snippets/s_faq_collapse.xml',
        'views/snippets/s_features_grid.xml',
        'views/snippets/s_features_wave.xml',
        'views/snippets/s_tabs.xml',
        'views/snippets/s_tabs_images.xml',
        'views/snippets/s_table_of_content.xml',
        'views/snippets/s_images_constellation.xml',
        'views/snippets/s_chart.xml',
        'views/snippets/s_parallax.xml',
        'views/snippets/s_quotes_carousel.xml',
        'views/snippets/s_numbers_grid.xml',
        'views/snippets/s_quotes_carousel_minimal.xml',
        'views/snippets/s_quotes_carousel_compact.xml',
        'views/snippets/s_numbers.xml',
        'views/snippets/s_contact_info.xml',
        'views/snippets/s_numbers_boxed.xml',
        'views/snippets/s_cta_box.xml',
        'views/snippets/s_numbers_framed.xml',
        'views/snippets/s_masonry_block.xml',
        'views/snippets/s_sidegrid.xml',
        'views/snippets/s_media_list.xml',
        'views/snippets/s_showcase.xml',
        'views/snippets/s_faq_horizontal.xml',
        'views/snippets/s_floating_blocks.xml',
        'views/snippets/s_opening_hours.xml',
        'views/snippets/s_timeline.xml',
        'views/snippets/s_timeline_images.xml',
        'views/snippets/s_timeline_list.xml',
        'views/snippets/s_process_steps.xml',
        'views/snippets/s_accordion.xml',
        'views/snippets/s_website_form_info.xml',
        'views/snippets/s_numbers_showcase.xml',
        'views/snippets/s_numbers_charts.xml',
        'views/snippets/s_accordion_image.xml',
        'views/snippets/s_pricelist_boxed.xml',
        'views/snippets/s_adventure.xml',
        'views/snippets/s_image_title.xml',
        'views/snippets/s_key_images.xml',
        'views/snippets/s_kickoff.xml',
        'views/snippets/s_discovery.xml',
        'views/snippets/s_bento_banner.xml',
        'views/snippets/s_striped.xml',
        'views/snippets/s_intro_pill.xml',
        'views/snippets/s_big_number.xml',
        'views/snippets/s_wavy_grid.xml',
        'views/snippets/s_images_mosaic.xml',
        'views/snippets/s_shape_image.xml',
        'views/snippets/s_empowerment.xml',
        'views/snippets/s_split_intro.xml',
        'views/snippets/s_text_highlight.xml',
        'views/snippets/s_pricelist_cafe.xml',
        'views/snippets/s_progress_bar.xml',
        'views/snippets/s_blockquote.xml',
        'views/snippets/s_badge.xml',
        'views/snippets/s_color_blocks_2.xml',
        'views/snippets/s_attributes_horizontal.xml',
        'views/snippets/s_product_list.xml',
        'views/snippets/s_mega_menu_multi_menus.xml',
        'views/snippets/s_mega_menu_menu_image_menu.xml',
        'views/snippets/s_mega_menu_thumbnails.xml',
        'views/snippets/s_mega_menu_little_icons.xml',
        'views/snippets/s_mega_menu_images_subtitles.xml',
        'views/snippets/s_mega_menu_menus_logos.xml',
        'views/snippets/s_mega_menu_odoo_menu.xml',
        'views/snippets/s_mega_menu_big_icons_subtitles.xml',
        'views/snippets/s_mega_menu_cards.xml',
        'views/snippets/s_google_map.xml',
        'views/snippets/s_cta_mockups.xml',
        'views/snippets/s_map.xml',
        'views/snippets/s_dynamic_snippet.xml',
        'views/snippets/s_dynamic_snippet_carousel.xml',
        'views/snippets/s_banner_product.xml',
        'views/snippets/s_embed_code.xml',
        'views/snippets/s_website_form.xml',
        'views/snippets/s_website_form_overlay.xml',
        'views/snippets/s_title_form.xml',
        'views/snippets/s_searchbar.xml',
        'views/snippets/s_inline_text.xml',
        'views/snippets/s_button.xml',
        'views/snippets/s_freegrid.xml',
        'views/snippets/s_card_offset.xml',
        'views/snippets/s_image.xml',
        'views/snippets/s_video.xml',
        'views/snippets/s_cta_badge.xml',
        'views/snippets/s_unveil.xml',
        'views/snippets/s_image_hexagonal.xml',
        'views/snippets/s_striped_center_top.xml',
        'views/snippets/s_numbers_list.xml',
        'views/snippets/s_quadrant.xml',
        'views/snippets/s_cta_card.xml',
        'views/snippets/s_image_frame.xml',
        'views/snippets/s_cta_mobile.xml',
        'views/snippets/s_reviews_wall.xml',
        'views/snippets/s_website_form_cover.xml',
        'views/snippets/s_form_aside.xml',
        'views/snippets/s_banner_connected.xml',
        'views/snippets/s_ecomm_categories_showcase.xml',
        'views/new_page_template_templates.xml',
        'views/website_views.xml',
        'views/website_pages_views.xml',
        'views/website_controller_pages_views.xml',
        'views/website_visitor_views.xml',
        'views/res_config_settings_views.xml',
        'views/website_rewrite.xml',
        'views/ir_actions_server_views.xml',
        'views/ir_asset_views.xml',
        'views/ir_attachment_views.xml',
        'views/ir_model_views.xml',
        'views/res_partner_views.xml',
        'views/neutralize_views.xml',
        'wizard/base_language_install_views.xml',
        'wizard/blocked_third_party_domains.xml',
        'wizard/website_robots.xml',
        # Replaces a post_init_hook that should be run on upgrade too.
        'data/update_theme_images.xml',
    ],
    'demo': [
        'data/website_demo.xml',
        'data/website_visitor_demo.xml',
    ],
    'application': True,
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
    'assets': {
        "mail.assets_public": [
            "website/static/src/**/common/**/*",
        ],
        'web.assets_frontend': [
            'html_builder/static/src/utils/scrolling.js',
            'website/static/src/interactions/**/*',
            'website/static/src/core/**/*',
            'website/static/src/utils/**/*',
            ('remove', 'website/static/src/interactions/**/*.edit.js'),
            # Multi-range is an opt-in feature.
            ('remove', 'website/static/src/interactions/multirange_input.js'),
            # Activated on-demand by website.ripple_effect_js.
            ('remove', 'website/static/src/interactions/ripple_effect.js'),
            ('remove', 'website/static/src/core/website_edit_service.js'),
            ('replace', 'web/static/src/legacy/js/public/public_root_instance.js', 'website/static/src/js/content/website_root_instance.js'),
            'website/static/src/snippets/**/*.js',
            ('remove', 'website/static/src/snippets/**/*.edit.js'),
            'website/static/src/libs/zoomodoo/zoomodoo.scss',
            'website/static/src/scss/website.scss',
            'website/static/src/scss/website_common.scss',
            'website/static/src/scss/website_controller_page.scss',
            'website/static/src/scss/website.ui.scss',
            'website/static/src/libs/zoomodoo/zoomodoo.js',
            'website/static/src/libs/bootstrap/bootstrap.js',
            'website/static/src/js/utils.js',
            'web/static/src/core/autocomplete/*',
            'website/static/src/components/autocomplete_with_pages/*',
            'website/static/src/js/tours/tour_utils.js',
            'website/static/src/js/content/website_root.js',
            'website/static/src/js/content/compatibility.js',
            'website/static/src/js/content/snippets.animation.js',
            'website/static/src/js/user_custom_javascript.js',
            'website/static/src/js/http_cookie.js',
            'website/static/src/xml/website.xml',
            'website/static/src/xml/website.background.video.xml',
            'website/static/src/xml/website.cookies_warning.xml',
            'website/static/src/js/text_processing.js',
            'website/static/src/js/highlight_utils.js',
            'website/static/src/client_actions/website_preview/website_builder_action.editor.scss',
            'website/static/src/components/user_switch.js',
        ],
        'web.assets_frontend_minimal': [
            'website/static/src/utils/misc.js',
            'website/static/src/js/content/inject_dom.js',
            'website/static/src/js/content/auto_hide_menu.js',
            'website/static/src/js/content/redirect.js',
            'website/static/src/js/content/adapt_content.js',
        ],
        'web.assets_frontend_lazy': [
            # Remove assets_frontend_minimal
            ('remove', 'website/static/src/utils/misc.js'),
            ('remove', 'website/static/src/js/content/inject_dom.js'),
            ('remove', 'website/static/src/js/content/auto_hide_menu.js'),
            ('remove', 'website/static/src/js/content/redirect.js'),
            ('remove', 'website/static/src/js/content/adapt_content.js'),
        ],
        'website.assets_edit_frontend': [
            'website/static/src/**/*.edit.js',
            'website/static/src/**/*.edit.scss',
            'website/static/src/**/*.edit.xml',
            'website/static/src/core/website_edit_service.js',
        ],
        'website.inside_builder_style': [
            ('include', 'html_builder.inside_builder_style'),
            'website/static/src/**/*.inside.scss',
        ],
        'web._assets_primary_variables': [
            'website/static/src/scss/primary_variables.scss',
            'website/static/src/scss/options/user_values.scss',
            'website/static/src/scss/options/colors/user_color_palette.scss',
            'website/static/src/scss/options/colors/user_gray_color_palette.scss',
            'website/static/src/scss/options/colors/user_theme_color_palette.scss',
        ],
        'web._assets_secondary_variables': [
            ('prepend', 'website/static/src/scss/secondary_variables.scss'),
        ],
        'web.assets_tests': [
            'website/static/tests/tour_utils/lifecycle_dep_interaction.js',
            'website/static/tests/tours/**/*',
            'website/static/src/client_actions/website_preview/website_builder_action_test_mode.js',
            'html_builder/static/src/utils/utils_css.js',
        ],
        'web.assets_backend': [
            ('include', 'website.assets_editor'),
            ('include', 'html_editor.assets_link_popover'),
            'website/static/src/scss/color_palettes.scss',
            'website/static/src/scss/view_hierarchy.scss',
            'website/static/src/scss/website.backend.scss',
            'website/static/src/scss/website_visitor_views.scss',
            'website/static/src/js/backend/**/*',
            'website/static/src/js/tours/tour_utils.js',
            'website/static/src/js/text_processing.js',
            'website/static/src/js/highlight_utils.js',
            'website/static/src/client_actions/*/*',
            ('remove', 'website/static/src/client_actions/website_preview/website_builder_action_test_mode.js'),
            'website/static/src/components/fields/*',
            'website/static/src/components/fullscreen_indication/fullscreen_indication.js',
            'website/static/src/components/fullscreen_indication/fullscreen_indication.scss',
            'website/static/src/components/fullscreen_indication/fullscreen_indication.xml',
            'website/static/src/components/website_loader/website_loader.js',
            'website/static/src/components/website_loader/website_loader.scss',
            'website/static/src/components/views/*',
            'website/static/src/services/website_service.js',
            'website/static/src/js/utils.js',
            'web/static/src/core/autocomplete/*',
            'website/static/src/components/autocomplete_with_pages/*',
            'website/static/src/xml/website.xml',
            'website/static/src/scss/website_controller_page_kanban.scss',
            'website/static/src/**/common/**/*',

            'website/static/src/xml/website_form_editor.xml',
            # TODO Remove the module's form js - this is for testing.
            'website/static/src/js/send_mail_form.js',
            # TODO when moving options to website: load this from website
            # directly. This file is loaded in assets_wysiwyg in website, but we
            # need to load it here for html_builder.
            'website/static/src/xml/website.cookies_bar.xml',
        ],
        "web.assets_web_dark": [
            'website/static/src/components/dialog/*.dark.scss',
            'website/static/src/scss/website.backend.dark.scss',
            'website/static/src/components/website_loader/website_loader.dark.scss'
        ],
        'web.assets_unit_tests': [
            'web/static/src/legacy/js/public/minimal_dom.js',
            'website/static/src/client_actions/website_preview/website_builder_action_test_mode.js',
            'website/static/tests/core/**/*',
            'website/static/tests/helpers.js',
            'website/static/tests/interactions/**/*',
            'website/static/tests/builder/**/*',
            ('include', 'website.website_builder_assets'),
            'website/static/tests/mock_server/**/*',
            'website/static/tests/redirect_field.test.js',
            'website/static/tests/new_content_modal.test.js',
        ],
        'web.assets_unit_tests_setup': [
            'html_builder/static/src/utils/scrolling.js',
            'web/static/src/legacy/js/core/class.js',
            'web/static/src/legacy/js/public/lazyloader.js',
            'web/static/src/legacy/js/public/minimal_dom.js',
            'web/static/src/legacy/js/public/public_widget.js',
            'web/static/src/legacy/js/public/public_root.js',
            'website/static/lib/multirange/*.js',
            'website/static/src/js/content/auto_hide_menu.js',
            'website/static/src/core/**/*',
            'website/static/src/utils/**/*',
            'website/static/src/interactions/**/*',
            'website/static/src/snippets/**/*.js',
            'website/static/src/snippets/**/*.xml',
            'website/static/src/xml/**/*.xml',
            ## TODO: remove the following line when cleaning up residuals files
            ## from the old editor
            ('remove', 'website/static/src/snippets/s_floating_blocks/options.xml'),
            'website/static/src/snippets/s_table_of_content/000.scss',
            'google_recaptcha/static/src/js/recaptcha.js',
        ],
        'web.tests_assets': [
            'website/static/tests/website_service_mock.js',
        ],
        'web._assets_frontend_helpers': [
            ('prepend', 'website/static/src/scss/bootstrap_overridden.scss'),
        ],
        'web._assets_bootstrap_frontend': [
            ('after', 'web/static/src/scss/utilities_custom.scss', 'html_builder/static/src/scss/utilities_custom.scss'),
        ],
        'html_editor.assets_link_popover': [
            'website/static/src/js/editor/html_editor.js',
            'website/static/src/xml/html_editor.xml',
        ],
        'website.assets_wysiwyg': [
            ('include', 'web._assets_helpers'),
            'web_editor/static/src/scss/bootstrap_overridden.scss',
            'web/static/src/scss/pre_variables.scss',
            'web/static/lib/bootstrap/scss/_variables.scss',
            'web/static/lib/bootstrap/scss/_variables-dark.scss',
            'web/static/lib/bootstrap/scss/_maps.scss',
            'website/static/src/scss/website.wysiwyg.scss',
            'website/static/src/scss/website.edit_mode.scss',
            'website/static/src/snippets/s_image_gallery/000.xml',
            'website/static/src/snippets/s_image_gallery/001.xml',
            'website/static/src/snippets/s_floating_blocks/options.xml',
            'website/static/src/js/send_mail_form.js',
            'website/static/src/xml/website_form.xml',
            'website/static/src/xml/website_form_editor.xml',
            'website/static/src/xml/website.cookies_bar.xml',
        ],
        'website.assets_all_wysiwyg': [
            ('include', 'website.assets_wysiwyg'),
        ],
        'website.backend_assets_all_wysiwyg': [
            ('include', 'web_editor.backend_assets_wysiwyg'),
            ('include', 'website.assets_wysiwyg'),
        ],
        'html_editor.assets_media_dialog': [
            'website/static/src/components/media_dialog/*',
        ],
        'website.assets_editor': [
            ('include', 'web._assets_helpers'),
            'web/static/src/scss/pre_variables.scss',
            'web/static/lib/bootstrap/scss/_variables.scss',
            'web/static/lib/bootstrap/scss/_variables-dark.scss',
            'web/static/lib/bootstrap/scss/_maps.scss',
            'website/static/src/components/resource_editor/**/*',
            'website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.js',
            'website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.scss',
            'website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml',
            'website/static/src/utils/**/*',
            'website/static/src/components/dialog/*.js',
            'website/static/src/components/dialog/*.scss',
            'website/static/src/components/dialog/*.xml',
            'website/static/src/components/editor/editor.js',
            'website/static/src/components/editor/editor.scss',
            'website/static/src/components/editor/editor.xml',
            'website/static/src/components/navbar/navbar.js',
            'website/static/src/components/navbar/navbar.scss',
            'website/static/src/components/navbar/navbar.xml',
            'website/static/src/components/burger_menu/burger_menu.js',
            'website/static/src/js/new_content_form.js',
            'website/static/src/services/website_custom_menus.js',
            'website/static/src/js/tours/homepage.js',
            'website/static/src/systray_items/*',
            'website/static/src/client_actions/*/*.xml',
            'website/static/src/components/website_loader/*.xml',
            'website/static/src/js/backend/**/*',

            # Don't include dark mode files in light mode
            ('remove', 'website/static/src/components/dialog/*.dark.scss'),
        ],
        'website.website_builder_assets': [
            ('include', 'html_builder.assets'),
            'website/static/src/scss/website_common.scss',
            'website/static/src/builder/**/*',
            ('remove', 'website/static/src/builder/**/*.inside.scss'),
        ],
        'html_builder.iframe_add_dialog': [
            'website/static/src/snippets/**/*.edit.scss',
        ],
    },
    'configurator_snippets': {
        'homepage': ['s_cover', 's_text_image', 's_numbers'],
        'about_us': ['s_text_image', 's_image_text', 's_title', 's_company_team'],
        'our_services': ['s_three_columns', 's_quotes_carousel', 's_references'],
        'pricing': ['s_comparisons'],
        'privacy_policy': ['s_faq_collapse'],
    },
    'new_page_templates': {
        'basic': {
            '1': ['s_text_block_h1', 's_text_block', 's_image_text', 's_text_image'],
            '2': ['s_text_block_h1', 's_picture', 's_text_block'],
            '3': ['s_parallax', 's_text_block_h1', 's_text_block', 's_three_columns'],
            '4': ['s_text_cover'],
            '5': ['s_text_block_h1', 's_text_block', 's_features', 's_quotes_carousel'],
            '6': ['s_text_block_h1', 's_table_of_content'],
        },
        'about': {
            'full': ['s_text_block_h1', 's_image_text', 's_text_image', 's_numbers', 's_picture', 's_quotes_carousel', 's_references'],
            'full_1': ['s_text_block_h1', 's_three_columns', 's_text_block_h2', 's_company_team', 's_references', 's_quotes_carousel', 's_call_to_action'],
            'mini': ['s_cover', 's_text_block_h2', 's_text_block_2nd', 's_picture_only', 's_text_block_h2_contact', 's_website_form'],
            'personal': ['s_text_cover', 's_image_text', 's_text_block_h2', 's_numbers', 's_features', 's_call_to_action_about'],
            'map': ['s_text_block_h1', 's_text_block', 's_numbers', 's_text_image', 's_text_block_h2', 's_text_block_2nd', 's_map', 's_images_wall'],
            'timeline': ['s_banner', 's_text_block_h2', 's_text_block', 's_timeline', 's_call_to_action_about'],
        },
        'landing': {
            '0': ['s_cover'],
            '1': ['s_banner', 's_features', 's_masonry_block_default_template', 's_call_to_action_digital', 's_references', 's_quotes_carousel'],
            '2': ['s_cover', 's_text_image', 's_text_block_h2', 's_three_columns', 's_call_to_action'],
            '3': ['s_text_cover', 's_text_block_h2', 's_three_columns', 's_showcase', 's_color_blocks_2', 's_quotes_carousel', 's_call_to_action'],
            '4': ['s_cover', 's_text_block_h2', 's_text_block', 's_text_block_h2_contact', 's_website_form'],
            '5': ['s_banner'],
        },
        'gallery': {
            '0': ['s_text_block_h1', 's_images_wall'],
            '1': ['s_text_block_h1', 's_image_text', 's_text_image', 's_image_text_2nd'],
            '2': ['s_banner', 's_text_block_2nd', 's_image_gallery', 's_picture_only'],
            '3': ['s_text_block_h1', 's_text_block', 's_three_columns', 's_three_columns_2nd'],
            '4': ['s_cover', 's_media_list'],
        },
        'services': {
            '0': ['s_text_block_h1', 's_text_block_2nd', 's_three_columns', 's_text_block_h2_contact', 's_website_form'],
            '1': ['s_text_block_h1', 's_features_grid', 's_text_block_h2', 's_faq_collapse', 's_call_to_action'],
            '2': ['s_text_cover', 's_image_text', 's_text_image', 's_image_text_2nd', 's_call_to_action_digital'],
            '3': ['s_text_block_h1', 's_parallax', 's_table_of_content', 's_call_to_action'],
        },
        'pricing': {
            '0': ['s_text_block_h1', 's_comparisons', 's_text_block_2nd', 's_showcase', 's_text_block_h2', 's_faq_collapse', 's_call_to_action'],
            '1': ['s_text_block_h1', 's_comparisons', 's_call_to_action'],
            '2': ['s_cover', 's_comparisons', 's_call_to_action', 's_features_grid', 's_color_blocks_2'],
            '3': ['s_carousel', 's_product_catalog', 's_call_to_action_menu'],  # should be s_call_to_action - but let's create that snippet
            '4': ['s_text_block_h1', 's_image_text', 's_text_image', 's_image_text_2nd', 's_call_to_action'],
            '5': ['s_text_block_h1', 's_text_block', 's_product_catalog', 's_three_columns_menu', 's_call_to_action'],  # was s_call_to_action_menu
        },
        'team': {
            '0': ['s_text_block_h1', 's_three_columns'],
            '1': ['s_text_block_h1', 's_image_text', 's_text_image', 's_image_text_2nd'],
            '2': ['s_text_block_h1', 's_company_team'],
            '3': ['s_text_block_h1', 's_media_list'],
            '4': ['s_text_block_h1', 's_text_block', 's_images_wall'],
            '5': ['s_text_block_h1', 's_text_block', 's_image_gallery', 's_picture'],
        },
    },
    'author': 'Odoo S.A.',
    'license': 'LGPL-3',
}
