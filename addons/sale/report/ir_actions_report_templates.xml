<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_saleorder_document">
    <t t-call="web.external_layout">
        <t t-set="doc" t-value="doc.with_context(lang=doc.partner_id.lang)" />
        <t t-set="forced_vat" t-value="doc.fiscal_position_id.foreign_vat"/> <!-- So that it appears in the footer of the report instead of the company VAT if it's set -->
        <t t-set="address">
            <div t-field="doc.partner_id" class="mb-0"
                t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
            <p t-if="doc.partner_id.vat" class="mb-0">
                <t t-if="doc.company_id.account_fiscal_country_id.vat_label" t-out="doc.company_id.account_fiscal_country_id.vat_label"/>
                <t t-else="">Tax ID</t>: <span t-field="doc.partner_id.vat"/>
            </p>
        </t>
        <t t-if="doc.partner_shipping_id == doc.partner_invoice_id
                             and doc.partner_invoice_id != doc.partner_id
                             or doc.partner_shipping_id != doc.partner_invoice_id">
            <t t-set="information_block">
                <strong>
                    <t t-if="doc.partner_shipping_id == doc.partner_invoice_id">
                        Invoicing and Shipping Address
                    </t>
                    <t t-else="">
                        Invoicing Address
                    </t>
                </strong>
                <div t-field="doc.partner_invoice_id"
                    t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                <t t-if="doc.partner_shipping_id != doc.partner_invoice_id">
                    <strong class="d-block mt-3">Shipping Address</strong>
                    <div t-field="doc.partner_shipping_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                </t>
            </t>
        </t>
        <div class="page">
            <div class="oe_structure"/>

            <t t-set="is_proforma" t-value="env.context.get('proforma', False) or is_pro_forma"/>
            <t t-set="layout_document_title">
                <span t-if="is_proforma">Pro-Forma Invoice # </span>
                <span t-elif="doc.state in ['draft','sent']">Quotation # </span>
                <span t-else="">Order # </span>
                <span t-field="doc.name">SO0000</span>
            </t>

            <div class="row mb-4" id="informations">
                <div t-if="doc.client_order_ref" class="col" name="informations_reference">
                    <strong>Your Reference</strong>
                    <div t-field="doc.client_order_ref">SO0000</div>
                </div>
                <div t-if="doc.date_order" class="col" name="informations_date">
                    <strong t-if="is_proforma">Issued Date</strong>
                    <strong t-elif="doc.state in ['draft', 'sent']">Quotation Date</strong>
                    <strong t-else="">Order Date</strong>
                    <div t-field="doc.date_order" t-options='{"widget": "date"}'>2023-12-31</div>
                </div>
                <div t-if="doc.validity_date and doc.state in ['draft', 'sent']"
                    class="col"
                    name="expiration_date">
                    <strong>Expiration</strong>
                    <div t-field="doc.validity_date">2023-12-31</div>
                </div>
                <div t-if="doc.commitment_date" class="col" name="delivery_date">
                    <strong>Delivery Date</strong>
                    <div
                        t-field="doc.commitment_date"
                        t-options="{'widget': 'date'}"
                    >
                        2023-12-31
                    </div>
                </div>
                <div t-if="doc.user_id.name" class="col">
                    <strong>Salesperson</strong>
                    <div t-field="doc.user_id">Mitchell Admin</div>
                </div>
            </div>

            <!-- Is there a discount on at least one line? -->
            <t t-set="lines_to_report" t-value="doc._get_order_lines_to_report()"/>
            <t t-set="display_discount" t-value="any(l.discount for l in lines_to_report)"/>
            <t t-set="display_taxes" t-value="any(l.tax_ids for l in lines_to_report)"/>

            <div class="oe_structure"></div>
            <table class="o_has_total_table table o_main_table table-borderless">
                <!-- In case we want to repeat the header, remove "display: table-row-group" -->
                <thead style="display: table-row-group">
                    <tr>
                        <th name="th_description" class="text-start">Description</th>
                        <th name="th_quantity" class="text-end text-nowrap">Quantity</th>
                        <th name="th_priceunit" class="text-end text-nowrap">Unit Price</th>
                        <th name="th_discount" t-if="display_discount" class="text-end">
                            <span>Disc.%</span>
                        </th>
                        <th name="th_taxes" t-if="display_taxes" class="text-end">
                            <span>Taxes</span>
                        </th>
                        <th name="th_subtotal" class="text-end">
                            <span>Amount</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="sale_tbody">

                    <t t-set="current_subtotal" t-value="0"/>

                    <t t-foreach="lines_to_report" t-as="line">

                        <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>

                        <t
                            t-if="line.display_type not in ('line_section', 'line_subsection')
                                or line.product_type == 'combo'
                                or not line.collapse_composition"
                        >
                        <tr
                            t-if="not line.collapse_composition"
                            t-att-class="'fw-bold o_line_section' if (
                                line.display_type == 'line_section'
                                or line.product_type == 'combo'
                            )
                            else 'fw-bold o_line_subsection' if line.display_type == 'line_subsection'
                            else 'fst-italic o_line_note' if line.display_type == 'line_note'
                            else ''"
                        >
                            <t t-if="not line.display_type and line.product_type != 'combo'">
                                <td name="td_name"><span t-field="line.name">Bacon Burger</span></td>
                                <td name="td_quantity" class="o_td_quantity text-end">
                                    <span t-field="line.product_uom_qty" class="text-nowrap">3</span>
                                    <span t-field="line.product_uom_id">units</span>
                                    <span t-if="line.product_uom_id != line.product_id.uom_id" class="text-muted small">
                                        <t t-set="quantity_in_product_uom" t-value="line.product_uom_id._compute_quantity(line.product_uom_qty, line.product_id.uom_id)"/>
                                        <br/><span t-esc="quantity_in_product_uom" t-options="{'widget': 'float', 'decimal_precision': 'Product Unit'}"/> <span t-field="line.product_id.uom_id"/>
                                    </span>
                                </td>
                                <td name="td_priceunit" class="text-end text-nowrap">
                                    <span
                                        t-if="not line.collapse_prices"
                                        t-field="line.price_unit"
                                    >
                                        3
                                    </span>
                                </td>
                                <td name="td_discount" t-if="display_discount" class="text-end">
                                    <span t-field="line.discount">-</span>
                                </td>
                                <t t-set="taxes" t-value="', '.join(tax.tax_label for tax in line.tax_ids if tax.tax_label)"/>
                                <td name="td_taxes" t-if="display_taxes" t-attf-class="text-end {{ 'text-nowrap' if len(taxes) &lt; 10 else '' }}">
                                    <span t-out="taxes">Tax 15%</span>
                                </td>
                                <td t-if="not line.is_downpayment" name="td_subtotal" class="text-end o_price_total">
                                    <span
                                        t-if="not line.collapse_prices"
                                        t-field="line.price_subtotal"
                                    >
                                        27.00
                                    </span>
                                </td>
                            </t>
                            <t
                                t-elif="line.display_type in ('line_section', 'line_subsection')
                                    or line.product_type == 'combo'"
                            >
                                <td name="td_section_line" colspan="99">
                                    <span t-field="line.name">A section title</span>
                                </td>
                                <t t-if="line.display_type == 'line_section'">
                                    <t t-set="current_section" t-value="line"/>
                                    <t t-set="current_subtotal" t-value="0"/>
                                </t>
                            </t>
                            <t t-elif="line.display_type == 'line_note'">
                                <td name="td_note_line" colspan="99">
                                    <span t-field="line.name">A note, whose content usually applies to the section or product above.</span>
                                </td>
                            </t>
                        </tr>
                        </t>

                        <t
                            t-elif="(
                                    line.display_type == 'line_subsection'
                                    and not line.parent_id.collapse_composition
                                )
                                or line.display_type == 'line_section'"
                        >
                            <tr
                                t-foreach="line._get_grouped_section_summary()"
                                t-as="section_line"
                                class="o_line_section"
                            >
                                <td name="td_name">
                                    <span t-out="section_line['name']">Section Summary</span>
                                </td>
                                <td name="td_quantity" class="text-end text-nowrap">
                                    <span>1.00 Units</span>
                                </td>
                                <td name="td_priceunit" class="text-end text-nowrap">
                                    <span t-out="section_line['price_subtotal']"/>
                                </td>
                                <td name="td_discount" t-if="display_discount"/>
                                <td
                                    name="td_taxes"
                                    t-if="display_taxes"
                                    t-attf-class="text-end {{ 'text-nowrap' if len(section_line['taxes']) &lt; 10 else '' }}"
                                >
                                    <span t-out="', '.join(section_line['taxes'])">
                                        Tax 15%
                                    </span>
                                </td>
                                <td class="text-end o_price_total">
                                    <span t-out="section_line['price_subtotal']">
                                        31.05
                                    </span>
                                </td>
                            </tr>
                            <t t-if="line.display_type == 'line_section'">
                                <t t-set="current_section" t-value="line"/>
                                <t t-set="current_subtotal" t-value="0"/>
                            </t>
                        </t>

                        <t
                            t-if="current_section and (
                                line_last
                                or lines_to_report[line_index+1].display_type == 'line_section'
                                or lines_to_report[line_index+1].product_type == 'combo'
                                or (
                                    line.combo_item_id
                                    and not lines_to_report[line_index+1].combo_item_id
                                )
                            )
                            and not line.is_downpayment
                            and not current_section.collapse_composition"
                        >
                            <t t-set="current_section" t-value="None"/>
                            <tr class="is-subtotal text-end">
                                <td name="td_section_subtotal" colspan="99">
                                    <strong class="mr16">Subtotal</strong>
                                    <span
                                        t-out="current_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                                    >31.05</span>
                                </td>
                            </tr>
                        </t>
                    </t>
                </tbody>
            </table>
            <div class="clearfix" name="so_total_summary">
                <div id="total" class="row mt-n3" name="total">
                    <div t-attf-class="#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'} ms-auto">
                        <table class="o_total_table table table-borderless">
                            <!-- Tax totals -->
                            <t t-call="sale.document_tax_totals">
                                <t t-set="tax_totals" t-value="doc.tax_totals"/>
                                <t t-set="currency" t-value="doc.currency_id"/>
                            </t>
                        </table>
                    </div>
                </div>
            </div>
            <div class="oe_structure"></div>

            <div t-if="not doc.signature" class="oe_structure"></div>
            <div t-else="" class="mt-4 ml64 mr4" name="signature">
                <div class="offset-8">
                    <strong>Signature</strong>
                </div>
                <div class="offset-8">
                    <img t-att-src="image_data_uri(doc.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                </div>
                <div class="offset-8 text-center">
                    <span t-field="doc.signed_by">Oscar Morgan</span>
                </div>
            </div>
            <div>
                <span t-field="doc.note" t-attf-style="#{'text-align:justify;text-justify:inter-word;' if doc.company_id.terms_type != 'html' else ''}" name="order_note"/>
                <p t-if="not is_html_empty(doc.payment_term_id.note)">
                    <span t-field="doc.payment_term_id.note">The payment should also be transmitted with love</span>
                </p>
                <div class="oe_structure"/>
                <p t-if="doc.fiscal_position_id and not is_html_empty(doc.fiscal_position_id.sudo().note)"
                    id="fiscal_position_remark">
                    <strong>Fiscal Position Remark:</strong>
                    <span t-field="doc.fiscal_position_id.sudo().note">No further requirements for this payment</span>
                </p>
            </div>
            <div class="oe_structure"/>
            <t t-set="base_address" t-value="doc.env['ir.config_parameter'].sudo().get_param('web.base.url')"/>
            <t t-set="portal_url" t-value="base_address + '/my/orders/' + str(doc.id) + '#portal_connect_software_modal_btn'"/>
            <div t-if="any(u._is_portal() for u in doc.partner_id.user_ids) and doc._get_edi_builders()" class="text-center">
                <a t-att-href="portal_url">Connect your software</a> with <t t-out="doc.company_id.name"/> to create quotes automatically.
            </div>
        </div>
    </t>
</template>

<template id="report_saleorder_raw">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="doc">
            <t t-call="sale.report_saleorder_document" t-lang="doc.partner_id.lang"/>
        </t>
    </t>
</template>

<template id="report_saleorder">
    <t t-call="sale.report_saleorder_raw"/>
</template>

<template id="report_saleorder_pro_forma">
    <t t-call="web.html_container">
        <t t-set="is_pro_forma" t-value="True"/>
        <t t-set="docs" t-value="docs.with_context(proforma=True)"/>
        <t t-foreach="docs" t-as="doc">
            <t t-call="sale.report_saleorder_document" t-lang="doc.partner_id.lang"/>
        </t>
    </t>
</template>

 <!-- Allow edits (e.g. studio) without changing the often inherited base template -->
<template id="document_tax_totals" inherit_id="account.document_tax_totals_template" primary="True"></template>

<template id="quote_document_layout_preview">
    <t t-call="web.html_preview_container">
        <t t-call="sale.report_saleorder_document"/>
    </t>
</template>

</odoo>
