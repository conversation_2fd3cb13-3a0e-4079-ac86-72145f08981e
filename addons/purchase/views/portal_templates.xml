<?xml version="1.0" encoding="utf-8"?>
<odoo>

  <template id="portal_my_home_menu_purchase" name="Portal layout : purchase menu entries" inherit_id="portal.portal_breadcrumbs" priority="25">
    <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
      <li t-if="page_name == 'rfq' or order and order.state == 'sent'" t-attf-class="breadcrumb-item #{'active ' if not order else ''}">
        <a t-if="order" t-attf-href="/my/rfq?{{ keep_query() }}">Requests for Quotation</a>
        <t t-else="">Requests for Quotation</t>
      </li>
      <li t-if="page_name == 'purchase' or order and order.state != 'sent'" t-attf-class="breadcrumb-item #{'active ' if not order else ''}">
        <a t-if="order" t-attf-href="/my/purchase?{{ keep_query() }}">Purchase Orders</a>
        <t t-else="">Purchase Orders</t>
      </li>
      <li t-if="order" class="breadcrumb-item active">
        <t t-esc="order.name"/>
      </li>
    </xpath>
  </template>

  <template id="portal_my_home_purchase" name="Requests for Quotation / Purchase Orders" customize_show="True" inherit_id="portal.portal_my_home" priority="25">
    <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
          <t t-set="portal_vendor_category_enable" t-value="True"/>
      </xpath>
      <div id="portal_vendor_category" position="inside">
          <t t-call="portal.portal_docs_entry">
              <t t-set="icon" t-value="'/web/static/img/rfq.svg'"/>
              <t t-set="text">Follow your Requests for Quotation</t>
              <t t-set="title">Requests for Quotation</t>
              <t t-set="url" t-value="'/my/rfq'"/>
              <t t-set="placeholder_count" t-value="'rfq_count'"/>
          </t>
          <t t-call="portal.portal_docs_entry">
              <t t-set="icon" t-value="'/purchase/static/src/img/calculator.svg'"/>
              <t t-set="text">Follow orders you have to fulfill</t>
              <t t-set="title">Our Orders</t>
              <t t-set="url" t-value="'/my/purchase'"/>
              <t t-set="placeholder_count" t-value="'purchase_count'"/>
          </t>
      </div>
  </template>

  <template id="portal_my_purchase_rfqs" name="My Requests For Quotation">
      <t t-call="portal.portal_layout">
          <t t-set="breadcrumbs_searchbar" t-value="True"/>

          <t t-call="portal.portal_searchbar">
              <t t-set="title" >Requests For Quotation</t>
          </t>
          <t t-if="not rfqs">
              <p class="alert alert-warning">There are currently no requests for quotation for your account.</p>
          </t>
          <t t-if="rfqs" t-call="portal.portal_table">
              <thead>
                  <tr class="active">
                    <th>
                        <span class='d-none d-md-inline'>Request for Quotation #</span>
                        <span class='d-block d-md-none'>Ref.</span>
                    </th>
                      <th class="text-end">Order Deadline</th>
                  </tr>
              </thead>
              <t t-foreach="rfqs" t-as="rfq">
                  <tr>
                      <td><a t-att-href="rfq.get_portal_url()"><t t-esc="rfq.name"/></a></td>
                      <td class="text-end">
                          <span t-field="rfq.date_order" t-options="{'widget': 'date'}"/>&amp;nbsp;
                          <span class='d-none d-md-inline' t-field="rfq.date_order" t-options="{'time_only': True}"/>
                      </td>
                  </tr>
              </t>
          </t>
      </t>
  </template>

  <template id="portal_my_purchase_orders" name="My Purchase Orders">
      <t t-call="portal.portal_layout">
          <t t-set="breadcrumbs_searchbar" t-value="True"/>

          <t t-call="portal.portal_searchbar">
              <t t-set="title">Purchase Orders</t>
          </t>
          <t t-if="not orders">
              <p class="alert alert-warning">There are currently no purchase orders for your account.</p>
          </t>
          <t t-if="orders" t-call="portal.portal_table">
              <thead>
                  <tr class="active">
                      <th id="order_name_header">
                          <span class='d-none d-md-inline'>Purchase Order #</span>
                          <span class='d-block d-md-none'>Ref.</span>
                      </th>
                      <th id="order_confirmation_header" class="text-end">
                          <span class='d-none d-md-inline'>Confirmation Date</span>
                          <span class='d-block d-md-none'>Confirmation</span>
                      </th>
                      <th id="order_badge_header" class="text-center"/>
                      <th id="order_total_header" class="text-end">Total</th>
                  </tr>
              </thead>
              <t t-foreach="orders" t-as="order">
                  <tr>
                      <td id="order_name"><a t-att-href="order.get_portal_url()"><t t-esc="order.name"/></a></td>
                      <td id="order_confirmation" class="text-end">
                          <span t-field="order.date_approve" t-options="{'widget': 'date'}"/>&amp;nbsp;
                          <span class='d-none d-md-inline' t-field="order.date_approve" t-options="{'time_only': True}"/>
                      </td>
                      <td id="order_badge" class="text-center">
                          <span t-if="order.invoice_status == 'to invoice'" class="badge rounded-pill text-bg-info">
                              <i class="fa fa-fw fa-file-text" role="img" aria-label="Waiting for Bill" title="Waiting for Bill"></i><span class="d-none d-md-inline"> Waiting for Bill</span>
                          </span>
                          <span t-if="order.state == 'cancel'" class="badge rounded-pill text-bg-secondary">
                              <i class="fa fa-fw fa-remove" role="img" aria-label="Cancelled" title="Cancelled"></i><span class="d-none d-md-inline"> Cancelled</span>
                          </span>
                      </td>
                      <td id="order_total" class="text-end"><span t-field="order.amount_total"/></td>
                  </tr>
              </t>
          </t>
      </t>
  </template>

  <template id="portal_my_purchase_order" name="Purchase Order" inherit_id="portal.portal_sidebar" primary="True">
      <xpath expr="//div[hasclass('o_portal_sidebar')]" position="inside">
          <t t-set="o_portal_fullwidth_alert" groups="purchase.group_purchase_manager">
              <t t-call="portal.portal_back_in_edit_mode">
                  <t t-set="backend_url" t-value="'/odoo/action-purchase.purchase_rfq/%s' % (order.id)"/>
              </t>
          </t>

          <div class="row o_portal_purchase_sidebar">
              <!-- Sidebar -->
              <t t-call="portal.portal_record_sidebar">
                  <t t-set="classes" t-value="'col-lg-4 col-xxl-3 d-print-none'"/>

                  <t t-set="title">
                      <h2 t-field="order.amount_total" data-id="total_amount" class="mb-0 text-break"/>
                  </t>
                  <t t-set="entries">
                      <div class="d-flex flex-column gap-4 mt-3">
                          <a class="btn btn-secondary o_print_btn o_portal_invoice_print" t-att-href="order.get_portal_url(report_type='pdf')" id="print_invoice_report" title="View Details" role="button" target="_blank"><i class="fa fa-print me-1"/>Download / Print</a>
                          <div class="navspy flex-grow-1 ps-0" t-ignore="true" role="complementary">
                              <ul class="nav flex-column bs-sidenav"></ul>
                          </div>
                          <div t-if="order.user_id">
                              <h6 class="small text-muted">Buyer</h6>
                                <t t-call="portal.portal_my_contact">
                                    <t t-set="_contactAvatar" t-value="image_data_uri(order.user_id.avatar_128)"/>
                                    <t t-set="_contactName" t-value="order.user_id.name"/>
                                    <t t-set="_contactLink" t-value="True"/>
                                    <div t-field="order.user_id" t-options='{"widget": "contact", "fields": ["phone"], "no_marker": False}'/>
                                </t>
                          </div>

                          <div t-if="order._get_edi_builders()">
                            <button id="portal_connect_software_modal_btn" type="button" class="btn btn-primary"
                            data-bs-toggle="modal" data-bs-target="#portal_connect_software_modal">
                                Connect with your software!
                            </button>
                          </div>
                      </div>
                  </t>
              </t>

              <div class="modal fade" id="portal_connect_software_modal" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                            <div>
                                <h5>Want to import this document in Odoo?</h5>
                                <p>Drag and drop the request for quotation PDF file into your list of quotations in Odoo. Enjoy the automation!</p>
                            </div>
                            <div>
                                <h5>Not using Odoo?</h5>
                                <div class="input-group mb-3 w-50">
                                    <t t-set="base_address" t-value="order.env['ir.config_parameter'].sudo().get_param('web.base.url')"/>
                                    <t t-set="doc_url" t-value="base_address + '/my/purchase/' + str(order.id) + '/download_edi?' + str(keep_query())"/>
                                    <input type="text" class="form-control" aria-describedby="copy-button" readonly="readonly" t-att-value="doc_url"/>
                                    <button class="btn btn-outline-secondary" type="button" id="copy-button"
                                    t-attf-onclick="navigator.clipboard.writeText('#{doc_url}')">Copy</button>
                                </div>
                                <p>Use the above REST URL to get structured data of the purchase order in UBL format.
                                    <a href="https://www.odoo.com/documentation/">Read the documentation </a>to learn all the ways to connect your software with <a href="https://www.odoo.com">Odoo</a>.
                                </p>
                            </div>
                            <div>
                                <h5>You don't use a good CRM software?</h5>
                                <p><a href="https://www.odoo.com/app/sales">Start on Odoo</a>, it's 100% free! You'll save time creating beautiful quotations and track sales.</p>
                            </div>
                        </div>
                        <div class="modal-footer justify-content-between">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
              </div>

              <!-- Page content -->
              <div id="quote_content" class="o_portal_content col-12 col-lg-8 col-xxl-9">
                  <!-- main content -->
                  <div id="portal_purchase_content">
                      <div t-call="purchase.purchase_order_portal_content"/>
                  </div>

                  <!-- chatter -->
                  <hr/>
                  <div id="purchase_order_communication">
                      <h3>Communication history</h3>
                      <t t-call="portal.message_thread"/>
                  </div>
              </div><!-- // #quote_content -->
          </div>
      </xpath>
  </template>

  <template id="purchase_order_portal_content" name="Purchase Order Portal Content">
      <!-- Intro -->
      <div id="introduction" class="mt-5 mt-lg-0 pb-2 pt-0">
        <h2 class="my-0">
          <t t-if="order.state in ['draft', 'sent']">Request for Quotation</t>
          <t t-else="1">
            Purchase Order
          </t>
          <em t-esc="order.name"/>
        </h2>
      </div>

      <div>
          <!-- Informations -->
          <div id="informations">
              <div class="row" id="po_date">
                  <div class="mb-3 col-6">
                    <t t-if="order.state in ['draft', 'sent']">
                      <strong>Request For Quotation Date:</strong>
                    </t>
                    <t t-if="order.state in ['purchase', 'cancel']">
                      <strong>Order Date:</strong>
                    </t>
                    <span t-field="order.date_order" t-options='{"widget": "date"}'/>
                  </div>
              </div>
              <div class="row">
                  <div class="col-lg-6">
                    <strong class="d-block mb-1">From:</strong>
                    <address t-field="order.company_id.partner_id" t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'/>
                    <t t-if="order.date_approve">
                      <strong>Confirmation Date:</strong> <span t-field="order.date_approve" t-options='{"widget": "date"}'/><br/>
                    </t>
                    <div t-att-class="'d-inline' if order.date_planned else 'd-none'">
                      <strong>Receipt Date:</strong><span class="ms-1" t-field="order.date_planned" t-options='{"widget": "date"}'/>
                    </div>
                  </div>
              </div>

              <t t-set="invoices" t-value="[i for i in order.invoice_ids if i.state not in ['draft', 'cancel']]"/>
              <div t-if="invoices" class="row">
                  <div class="col">
                      <strong class="d-block mb-1">Invoices</strong>
                      <ul class="list-group mb-4">
                          <t t-foreach="invoices" t-as="i">
                              <t t-set="report_url" t-value="i.get_portal_url(report_type='pdf', download=True)"/>
                              <div class="d-flex flex-wrap align-items-center justify-content-between">
                                  <div>
                                      <a t-att-href="report_url">
                                          <span t-esc="i.name"/>
                                      </a>
                                      <div class="small d-lg-inline-block">Date: <span class="text-muted" t-field="i.invoice_date"/></div>
                                  </div>
                                  <span t-if="i.payment_state in ('paid', 'in_payment')" class="badge rounded-pill text-bg-success small"><i class="fa fa-fw fa-check"/>Paid</span>
                                  <span t-elif="i.payment_state == 'reversed'" class="badge rounded-pill text-bg-success small"><i class="fa fa-fw fa-check"/>Reversed</span>
                                  <span t-else="" class="small badge rounded-pill text-bg-primary"><i class="fa fa-fw fa-clock-o"/>Waiting Payment</span>
                              </div>
                          </t>
                      </ul>
                  </div>
              </div>
          </div>

          <section id="details" style="page-break-inside: auto;" class="mt32">
              <h3 id="details">Pricing</h3>

              <div class="table-responsive">
                <table t-att-data-order-id="order.id" t-att-data-token="order.access_token" class="table table-sm" id="purchase_order_table">
                    <t t-set="display_price_and_taxes" t-value="not update_dates and order.state == 'purchase'"/>
                    <thead class="bg-100">
                        <tr>
                            <th class="text-start">Products</th>
                            <th class="text-end">Quantity</th>
                            <th t-if="update_dates" class="text-end">Scheduled Date</th>
                            <th t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">Unit Price</th>
                            <th t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                <span>Taxes</span>
                            </th>
                            <th t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">Disc.%</th>
                            <th class="text-end" t-if="display_price_and_taxes" >
                                <span>Amount</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="purchase_tbody">

                          <t t-set="current_subtotal" t-value="0"/>

                          <t t-foreach="order.order_line.filtered(lambda l: l.display_type or l.product_qty != 0)" t-as="line">

                              <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>

                            <tr t-att-class="'fw-bolder o_line_section' if line.display_type == 'line_section' else 'fw-bold o_line_subsection' if line.display_type == 'line_subsection' else 'fst-italic text-break o_line_note' if line.display_type == 'line_note' else ''">
                                <t t-if="not line.display_type">
                                    <td id="product_name" class="d-flex">
                                        <img t-att-src="line.product_id.image_128 and image_data_uri(line.product_id.image_128) or '/web/static/img/placeholder.png'"
                                            alt="Product" class="d-none d-lg-inline o_purchase_portal_product_image"/>
                                        <span t-field="line.name"/>
                                    </td>
                                    <td class="text-end">
                                        <div id="quote_qty">
                                            <span t-field="line.product_qty"/>
                                            <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                        </div>
                                    </td>
                                    <td t-if="update_dates" class="text-end">
                                        <div class="container">
                                          <div class="mb-3">
                                            <div class="input-group date">
                                                <input type="text"
                                                    class="form-control datetimepicker-input o-purchase-datetimepicker text-end"
                                                    t-att-data-access-token="order.access_token"
                                                    t-att-data-order-id="order.id"
                                                    t-att-data-line-id="line.id"
                                                    t-att-data-value="line.date_planned.isoformat()"
                                                />
                                            </div>
                                          </div>
                                        </div>
                                    </td>
                                    <td t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                        <div
                                            t-field="line.price_unit"
                                            class="text-end"
                                        />
                                    </td>
                                    <td t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span t-out="', '.join(map(lambda x: (x.name), line.tax_ids))"/>
                                    </td>
                                    <td t-if="display_price_and_taxes" t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                        <div t-field="line.discount" class="text-end"/>
                                    </td>
                                    <td class="text-end" t-if="not update_dates and order.state == 'purchase'">
                                        <span class="oe_order_line_price_subtotal" t-field="line.price_subtotal"/>
                                    </td>
                                </t>
                                <t t-if="line.display_type in ('line_section', 'line_subsection')">
                                    <td colspan="99">
                                        <span t-field="line.name"/>
                                    </td>
                                    <t t-set="current_section" t-value="line"/>
                                    <t t-set="current_subtotal" t-value="0"/>
                                </t>
                                <t t-if="line.display_type == 'line_note'">
                                    <td colspan="99">
                                        <span t-field="line.name"/>
                                    </td>
                                </t>
                            </tr>
                            <t t-if="current_section and (line_last or order.order_line[line_index+1].display_type in ('line_section', 'line_subsection')) and order.state == 'purchase'">
                                <tr class="is-subtotal text-end">
                                    <td colspan="99">
                                        <strong class="mr16">Subtotal</strong>
                                        <span
                                            t-esc="current_subtotal"
                                            t-options='{"widget": "monetary", "display_currency": order.currency_id}'
                                        />
                                    </td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>
              </div>

              <div id="total" t-if="order.state == 'purchase'" class="row" name="total" style="page-break-inside: avoid;">
                  <div t-attf-class="#{'col-4' if report_type != 'html' else 'col-sm-7 col-md-5'} ms-auto">
                      <t t-call="purchase.purchase_order_portal_content_totals_table"/>
                  </div>
              </div>
          </section>

          <section id="terms" class="mt-5" t-if="order.note">
              <h3 class="">Terms &amp; Conditions</h3>
              <hr class="mt-0 mb-1"/>
              <em t-field="order.note"/>
          </section>

          <section class="mt-5" t-if="order.payment_term_id">
              <h3 class="">Payment terms</h3>
              <hr class="mt-0 mb-1"/>
              <span t-field="order.payment_term_id"/>
          </section>
      </div>
  </template>

  <template id="purchase_order_portal_content_totals_table">
      <table class="table table-sm">
          <t t-call="purchase.document_tax_totals">
                <t t-set="tax_totals" t-value="order.tax_totals"/>
                <t t-set="currency" t-value="order.currency_id"/>
          </t>
      </table>
  </template>

  <template id="portal_my_purchase_order_update_date" name="Portal: My Purchase Order Update Dates" inherit_id="purchase.portal_my_purchase_order" primary="True">
    <xpath expr="////div[@id='portal_purchase_content']" position="replace">
      <div id="portal_purchase_content">
        <t t-set="update_dates" t-value="True"/>
        <div t-call="purchase.purchase_order_portal_content"/>
      </div>
    </xpath>
  </template>

</odoo>
