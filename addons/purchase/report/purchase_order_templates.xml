<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_purchaseorder_document">
    <t t-call="web.external_layout">
        <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang)"/>
        <t t-set="address">
            <div t-field="o.partner_id"
                 t-options='{"widget": "contact", "fields": ["address", "name", "phone", "vat"], "no_marker": True, "phone_icons": True}'/>
        </t>
        <t t-if="o.dest_address_id">
            <t t-set="information_block">
                <strong class="d-block mt-3">Shipping address</strong>
                <div t-if="o.dest_address_id">
                    <div t-field="o.dest_address_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}' name="purchase_shipping_address"/>
                </div>

            </t>
        </t>
        <div class="page">
            <div class="oe_structure"/>

            <div class="mt-4">
                <t t-set="layout_document_title">
                    <t t-if="o.state in ['draft', 'sent', 'to approve']">Request for Quotation #<span t-field="o.name"/></t>
                    <t t-if="o.state == 'purchase'">Purchase Order #<span t-field="o.name"/></t>
                    <t t-if="o.state == 'cancel'">Cancelled Purchase Order #<span t-field="o.name"/></t>
                </t>
            </div>

            <div id="informations" class="row mb-4">
                <div t-if="o.user_id" class="col">
                    <strong>Buyer</strong>
                    <div t-field="o.user_id"/>
                </div>
                <div t-if="o.partner_ref" class="col">
                    <strong>Your Order Reference</strong>
                    <div t-field="o.partner_ref"/>
                </div>
                <div t-if="o.state == 'purchase' and o.date_approve" class="col-3 bm-2">
                    <strong>Order Date:</strong>
                    <p t-field="o.date_approve" t-options="{'date_only': 'true'}" class="m-0"/>
                </div>
                <div t-elif="o.date_order" class="col-2 bm-2">
                    <strong>Order Deadline:</strong>
                    <p t-field="o.date_order" t-options="{'date_only': 'true'}" class="m-0"/>
                </div>
                <div t-if="o.date_planned" class="col-2 bm-2">
                    <strong>Expected Arrival:</strong>
                    <p t-field="o.date_planned" t-options="{'date_only': 'true'}" class="m-0"/>
                </div>
            </div>

            <table class="o_has_total_table table o_main_table table-borderless">
                <thead style="display: table-row-group">
                    <tr>
                        <th name="th_description" class="text-start"><strong>Description</strong></th>
                        <th name="th_quantity" class="text-end"><strong>Qty</strong></th>
                        <th name="th_price_unit" class="text-end"><strong>Unit Price</strong></th>
                        <th name="th_discount" class="text-end"><strong>Disc.</strong></th>
                        <th name="th_taxes" class="text-end"><strong>Taxes</strong></th>
                        <th name="th_subtotal" class="text-end">
                            <strong>Amount</strong>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <t t-set="current_subtotal" t-value="0"/>
                    <t t-foreach="o.order_line.filtered(lambda l: l.display_type or l.product_qty != 0)" t-as="line">
                        <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>

                        <tr t-att-class="'fw-bolder o_line_section' if line.display_type == 'line_section' else 'fw-bold o_line_subsection' if line.display_type == 'line_subsection' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                            <t t-if="not line.display_type">
                                <td id="product" class="text-start">
                                    <span t-field="line.name"/>
                                </td>
                                <td class="text-end">
                                    <span t-field="line.product_qty"/>
                                    <span t-field="line.product_uom_id.name" groups="uom.group_uom"/>
                                    <span t-if="line.product_uom_id != line.product_id.uom_id" class="text-muted small">
                                        <br/>
                                        <span t-field="line.product_uom_qty" t-options="{'widget': 'float', 'decimal_precision': 'Product Unit'}"/> <span t-field="line.product_id.uom_id"/>
                                    </span>
                                </td>
                                <td class="text-end">
                                    <span t-field="line.price_unit"/>
                                </td>
                                <td class="text-end">
                                    <span class="text-align-bottom"><span t-field="line.discount"/>%</span>
                                </td>
                                <td class="text-end">
                                    <span t-out="', '.join(tax.tax_label for tax in line.tax_ids if tax.tax_label)"/>
                                </td>
                                <td class="text-end">
                                    <span t-field="line.price_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                </td>
                            </t>
                            <t t-if="line.display_type in ('line_section', 'line_subsection')">
                                <td colspan="99" id="section">
                                    <span t-field="line.name"/>
                                </td>
                                <t t-set="current_section" t-value="line"/>
                                <t t-set="current_subtotal" t-value="0"/>
                            </t>
                            <t t-if="line.display_type == 'line_note'">
                                <td colspan="99" id="note">
                                    <span t-field="line.name"/>
                                </td>
                            </t>
                        </tr>
                        <t t-if="current_section and (line_last or o.order_line[line_index+1].display_type in ('line_section', 'line_subsection'))">
                            <tr class="is-subtotal text-end">
                                <td colspan="99" id="subtotal">
                                    <strong class="mr16">Subtotal</strong>
                                    <span
                                        t-out="current_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": o.currency_id}'
                                    />
                                </td>
                            </tr>
                        </t>
                    </t>
                </tbody>
            </table>

            <div id="total" class="row justify-content-end mt-n3">
                <div class="col-4">
                    <table class="o_total_table table table-borderless">
                        <t t-call="purchase.document_tax_totals">
                            <t t-set="tax_totals" t-value="o.tax_totals"/>
                            <t t-set="currency" t-value="o.currency_id"/>
                        </t>
                    </table>
                </div>
            </div>

            <p t-field="o.note" class="mt-4"/>
            <div class="oe_structure"/>

            <strong>Payment Terms: </strong>
            <span t-field="o.payment_term_id" class="mt-4"></span>

            <t t-set="base_address" t-value="o.env['ir.config_parameter'].sudo().get_param('web.base.url')"/>
            <t t-set="portal_url" t-value="base_address + '/my/purchase/' + str(o.id) + '#portal_connect_software_modal'"/>
            <div t-if="any(u._is_portal() for u in o.partner_id.user_ids) and o._get_edi_builders()" class="text-center">
                <a t-att-href="portal_url">Connect your software</a> with <t t-out="o.company_id.name"/> to create quotes automatically.
            </div>
        </div>
    </t>
</template>

<template id="report_purchaseorder">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="purchase.report_purchaseorder_document" t-lang="o.partner_id.lang"/>
        </t>
    </t>
</template>

 <!-- Allow edits (e.g. studio) without changing the often inherited base template -->
<template id="document_tax_totals" inherit_id="account.document_tax_totals_template" primary="True"></template>

</odoo>
