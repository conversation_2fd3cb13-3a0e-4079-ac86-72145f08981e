<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="im_livechat.LivechatChannelInfoList">
        <ActionPanel title.translate="Information" minWidth="200" initialWidth="300" icon="'fa fa-info'">
            <a t-if="visitorProfileURL" class="btn btn-primary mt-1" t-on-click="openVisitorProfile" t-att-href="visitorProfileURL" title="View Contact">
                View Contact
            </a>
            <div t-if="!props.thread.livechat_end_dt" class="o-livechat-LivechatChannelInfoList d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Status</h3>
                <t t-call="im_livechat.LivechatStatusSelection">
                    <t t-set="livechatThread" t-value="props.thread"/>
                </t>
            </div>
            <div t-if="props.thread.livechat_end_dt" class="o-livechat-LivechatChannelInfoList d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Outcome</h3>
                <div class="d-flex text-truncate">
                    <t t-if="props.thread.livechat_outcome === 'no_answer'">Never Answered</t>
                    <t t-elif="props.thread.livechat_outcome === 'no_agent'">No one Available</t>
                    <t t-elif="props.thread.livechat_outcome === 'no_failure'">Success</t>
                    <t t-elif="props.thread.livechat_outcome === 'escalated'">Escalated</t>
                </div>
            </div>
            <div class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Notes</h3>
                <textarea class="form-control" rows="3" placeholder="Add your notes here..." t-model="props.thread.livechatNoteText" t-on-blur="onBlurNote"></textarea>
            </div>
            <div t-if="expectAnswerSteps.length" class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Chatbot answers</h3>
                <t t-foreach="expectAnswerSteps" t-as="step" t-key="step.id">
                    <div class="d-flex align-items-center gap-1 bg-inherit rounded-3">
                        <i class="fa fa-comment-o me-1"/>
                        <span class="text-truncate" t-esc="step.answer"/>
                    </div>
                </t>
            </div>
            <div t-if="expertiseTags.length" class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Expertise</h3>
                <div class="d-flex flex-wrap gap-1">
                    <TagsList displayText="true" tags="expertiseTags"/>
                </div>
            </div>
            <t t-name="extra_infos"/>
            <div t-if="props.thread.livechat_end_dt" class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Send conversation</h3>
                <TranscriptSender thread="props.thread"/>
            </div>
        </ActionPanel>
    </t>

    <t t-name="im_livechat.LivechatChannelInfoList.info_links">
        <div t-if="templateParams.info_records.length" class="d-flex flex-column bg-inherit gap-1">
            <h3 class="pt-3" t-out="templateParams.title"/>
            <t t-foreach="templateParams.info_records" t-as="record" t-key="record.localId">
                <a
                    class="btn btn-sm btn-secondary d-flex align-items-center justify-content-start gap-1 px-3 py-1 rounded-3"
                    t-att-href="record.href"
                    t-att-data-oe-id="record.id"
                    t-att-data-oe-model="templateParams.model"
                    contenteditable="false"
                    target="_blank"
                >
                    <i class="fa fa-external-link opacity-75"/>
                    <span class="ms-1 fw-bold" t-esc="record.name"/>
                </a>
            </t>
        </div>
    </t>
</templates>
