<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.ControlButtons">
        <!-- All buttons always displayed -->
        <SelectPartnerButton partner="partner" t-if="!props.showRemainingButtons"/>
        <t t-if="!props.showRemainingButtons || (ui.isSmall and props.showRemainingButtons)">
            <InternalNoteButton label.translate="Note" class="buttonClass"/>
        </t>
        <button t-if="pos.config.use_presets and !props.showRemainingButtons"
            class="btn btn-light btn-lg flex-shrink-0 border-0"
            t-attf-class="{{`o_colorlist_item_color_${currentOrder.preset_id?.color}`}}"
            t-on-click="() => this.pos.selectPreset()">
            <span t-esc="currentOrder.preset_id?.name" />
        </button>
        <button class="btn btn-secondary btn-lg flex-shrink-0 ms-auto more-btn" t-if="!props.showRemainingButtons and !ui.isSmall and props.onClickMore" t-on-click="props.onClickMore">
            <i class="oi oi-fw oi-ellipsis-v" aria-hidden="true" />
        </button>
        <!-- All these buttons will only be displayed in a dialog -->
        <t t-if="props.showRemainingButtons">
            <div class="control-buttons control-buttons-modal d-grid gap-2 mt-2">
                <NoteButton
                    label.translate="Customer Note"
                    class="buttonClass"
                    icon="'fa fa-sticky-note'"/>
                <button t-if="this.pos.cashier._role != 'minimal'" class="o_pricelist_button" t-att-class="buttonClass" t-on-click="() => this.clickPricelist()">
                    <i class="fa fa-th-list me-2" role="img" aria-label="Price list" title="Price list" />
                    <t t-if="currentOrder?.pricelist_id" t-esc="currentOrder.pricelist_id.display_name" />
                    <t t-else="">Pricelist</t>
                </button>
                <button t-if="this.pos.cashier._role != 'minimal'" t-att-class="buttonClass" t-on-click="() => this.clickRefund()">
                    <i class="fa fa-undo me-1" role="img" aria-label="Refund" title="Refund" />
                    Refund
                </button>
                <button t-if="pos.models['account.fiscal.position'].length and this.pos.cashier._role != 'minimal'"
                    class="control-button o_fiscal_position_button"
                    t-att-class="buttonClass"
                    t-on-click="() => this.clickFiscalPosition()">
                    <i class="fa fa-book me-1" role="img" aria-label="Set fiscal position"
                    title="Set fiscal position" />
                    <t t-if="currentOrder?.fiscal_position_id" t-esc="currentOrder.fiscal_position_id.display_name" />
                    <t t-else="">Tax</t>
                </button>
                <button t-if="this.displayProductInfoBtn()" t-att-class="buttonClass" t-on-click="() => this.pos.onProductInfoClick(currentOrder.getSelectedOrderline().product_id.product_tmpl_id)">
                    <i class="fa fa-info-circle me-1" role="img" aria-label="Product Info" title="Product Info" /> Info
                </button>
                <button t-if="this.pos.cashier._role != 'minimal'" t-att-class="buttonClass" t-on-click="() => this.pos.onDeleteOrder(this.pos.getOrder())">
                    <i class="fa fa-trash me-1" role="img" /> Cancel Order 
                </button>
            </div>
        </t>
    </t>
    <t t-name="point_of_sale.ControlButtonsPopup">
        <Dialog bodyClass="'d-flex flex-column'" footer="false" title.translate="Actions" t-on-click="props.close">
            <ControlButtons showRemainingButtons="true" close="props.close"/>
        </Dialog>
    </t>
</templates>
