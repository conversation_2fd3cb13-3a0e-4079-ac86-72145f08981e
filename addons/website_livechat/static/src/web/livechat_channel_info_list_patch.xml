<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="website_livechat.LivechatChannelInfoList" t-inherit="im_livechat.LivechatChannelInfoList" t-inherit-mode="extension">
        <xpath expr="//t[@t-name='extra_infos']" position="inside">
            <t t-set="visitor" t-value="props.thread.livechat_visitor_id"/>
            <div t-if="visitor?.history" class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Recent page views</h3>
                <div t-if="visitor.website_id">
                    <i class="me-1 fa fa-globe" aria-label="Website"/>
                    <span t-esc="visitor.website_id.name"/>
                </div>
                <span t-esc="visitor.history"/>
            </div>
            <div t-if="!props.thread.livechat_end_dt and recentConversations.length" class="d-flex flex-column bg-inherit gap-1">
                <h3 class="pt-3">Recent conversations</h3>
                <div class="btn btn-group o-rounded-bubble d-flex flex-column w-100 p-0 m-0" style="gap: 1px;">
                    <a
                        t-foreach="recentConversations" t-as="thread" t-key="thread.localId"
                        class="btn btn-sm btn-secondary btn-group-item d-flex align-items-center justify-content-start gap-1 w-100 m-0 px-3 py-1"
                        t-attf-href="/odoo/discuss?active_id={{thread.model}}_{{thread.id}}"
                        target="_blank"
                        t-att-class="{
                            'o-rounded-top-bubble': thread_first,
                            'o-rounded-bottom-bubble': thread_last,
                            'rounded-top-0': !thread_first,
                            'rounded-bottom-0 border-bottom': !thread_last,
                        }"
                    >
                        <i class="fa fa-external-link opacity-75"/>
                        <span class="ms-1 fw-bold" t-esc="thread.displayName"/>
                        <span t-if="thread.livechat_end_dt" class="o-xsmaller text-muted" t-out="CLOSED_ON_TEXT(thread)"/>
                    </a>
                </div>
            </div>
        </xpath>
    </t>
</templates>
