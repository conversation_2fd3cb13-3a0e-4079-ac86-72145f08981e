<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="website_livechat.Thread" t-inherit="mail.Thread" t-inherit-mode="extension">
        <xpath expr="//*[hasclass('o-mail-Thread')]" position="before">
            <t t-set="visitor" t-value="props.thread.livechat_visitor_id"/>
            <div t-if="visitor and !env.inChatWindow" class="o-website_livechat-VisitorBanner py-2 px-2 d-flex border-bottom">
                <div t-if="props.thread.correspondent" class="o-website_livechat-VisitorBanner-sidebar me-2 d-flex justify-content-center">
                    <img class="rounded o-website_livechat-VisitorBanner-avatar object-fit-cover" t-att-src="props.thread.correspondent.avatarUrl" alt="Avatar"/>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2 fw-bolder" t-esc="visitor.display_name"/>
                    <img t-if="visitor.country" class="me-2 o_country_flag align-self-center" t-att-src="visitor.country.flagUrl" t-att-alt="visitor.country.code or visitor.country.name"/>
                    <span t-if="visitor.lang_id" class="me-2">
                        <i class="me-1 fa fa-comment-o" aria-label="Lang"/>
                        <t t-esc="visitor.lang_id.name"/>
                    </span>
                </div>
            </div>
        </xpath>
    </t>
</templates>
