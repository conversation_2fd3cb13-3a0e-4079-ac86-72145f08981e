<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_resource_calendar_search" model="ir.ui.view">
        <field name="name">resource.calendar.search</field>
        <field name="model">resource.calendar</field>
        <field name="arch" type="xml">
            <search string="Search Working Time">
               <field name="name" string="Working Time"/>
               <field name="company_id" groups="base.group_multi_company"/>
               <separator/>
               <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
               <filter string="Partial working schedules" name="partial_working_schedules" domain="[('work_time_rate', '&lt;', 100)]"/>
               <group>
                    <filter string="Flexible" name="groupby_flexible_hours" context="{'group_by': 'flexible_hours'}"/>
                    <filter name="groupby_company" context="{'group_by': 'company_id'}"/>
                </group>
           </search>
        </field>
    </record>

    <record id="resource_calendar_form" model="ir.ui.view">
        <field name="name">resource.calendar.form</field>
        <field name="model">resource.calendar</field>
        <field name="arch" type="xml">
            <form string="Working Time">
                <header>
                </header>
                <sheet string="Working Time">
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(resource_calendar_leaves_action_from_calendar)d" type="action"
                                icon="fa-plane"
                                class="oe_stat_button"
                                groups="base.group_no_one">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Time Off</span>
                                </div>
                        </button>
                        <button name="%(resource_resource_action_from_calendar)d" type="action"
                                icon="fa-cogs"
                                class="oe_stat_button"
                                groups="base.group_user">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">
                                        <field name="work_resources_count" string="Work Resources" widget="statinfo"/>
                                    </span>
                                </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1><field name="name"/></h1>
                    </div>
                    <group name="resource_details" col="2">
                        <group name="resource_working_hours">
                            <field name="schedule_type" widget="radio"/>
                            <label for="full_time_required_hours" invisible="not company_id"/>
                            <label for="full_time_required_hours" string="Required Full Time" invisible="company_id"/>
                            <div>
                                <field name="full_time_required_hours" style="width: 6ch;" widget="float_time" readonly="company_id"/>
                                <span>hours/week</span>
                            </div>
                            <label for="work_time_rate" string="Work Time Rate"/>
                            <div>
                                <field name="work_time_rate" nolabel="1" style="width: 6ch;"/>
                                <span class="ms-1">%</span>
                            </div>
                        </group>
                        <group>
                            <group name="resource_company" colspan="2">
                                <field name="company_id" placeholder="Visible to all"/>
                                <field name="tz" widget="timezone_mismatch" options="{'tz_offset_field': 'tz_offset', 'mismatch_title': 'Timezone Mismatch : This timezone is different from that of your browser.\nPlease, be mindful of this when setting the working hours or the time off.'}" />
                                <field name="tz_offset" invisible="1"/> <!-- Computed field needed for timezone_mismatch widget -->
                            </group>
                            <group name="flex_details" invisible="not flexible_hours" colspan="2">
                                <label for="hours_per_day" string="Avg"/>
                                <div class="d-flex">
                                    <field name="hours_per_day" nolabel="1" style="width: 6ch;" widget="float_time"/>
                                    <span class="ms-2">hours/day</span>
                                </div>
                                <label for="hours_per_week" string="Total"/>
                                <div class="d-flex">
                                    <field name="hours_per_week" nolabel="1" style="width: 6ch;" widget="float_time"/>
                                    <span class="ms-2">hours/week</span>
                                </div>
                            </group>
                            <group name="switch_calendar" invisible="flexible_hours">
                                <label for="switch_calendar_type" string="2 Week's Calendar" class="o_form_label"/>
                                <button name="switch_calendar_type" invisible="two_weeks_calendar" type="object"
                                    icon="fa-square-o" title="Switch to 2-weeks calendar" class="btn p-0"
                                    confirm="Are you sure you want to switch to a 2-week calendar? All work entries will be lost."
                                    confirm-label="Switch"/>
                                <button name="switch_calendar_type" invisible="not two_weeks_calendar" type="object"
                                    icon="fa-check-square" title="Switch to 1-week calendar" class="btn p-0"
                                    confirm="Are you sure you want to switch to a 1-week calendar? All work entries will be lost."
                                    confirm-label="Switch"/>
                            </group>
                        </group>
                    </group>
                    <notebook>
                        <page string="Working Hours" name="working_hours" invisible="flexible_hours or two_weeks_calendar">
                            <group>
                                <group>
                                    <label for="hours_per_day" string="Avg"/>
                                    <div class="d-flex">
                                        <field name="hours_per_day" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/day</span>
                                    </div>
                                </group>
                                <group>
                                    <label for="hours_per_week" string="Total"/>
                                    <div class="d-flex">
                                        <field name="hours_per_week" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/week</span>
                                    </div>
                                </group>
                            </group>
                            <field name="attendance_ids" widget="section_one2many"/>
                        </page>
                        <page string="Week 1 Working Hours" name="working_hours" invisible="flexible_hours or not two_weeks_calendar">
                            <field name="two_weeks_explanation"/>
                            <group>
                                <group>
                                    <label for="hours_per_day" string="Avg"/>
                                    <div class="d-flex">
                                        <field name="hours_per_day" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/day</span>
                                    </div>
                                </group>
                                <group>
                                    <label for="hours_per_week" string="Total"/>
                                    <div class="d-flex">
                                        <field name="hours_per_week" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/week</span>
                                    </div>
                                </group>
                            </group>
                            <field name="attendance_ids_1st_week" widget="section_one2many"/>
                        </page>
                        <page string="Week 2 Working Hours" name="working_hours" invisible="flexible_hours or not two_weeks_calendar">
                            <field name="two_weeks_explanation"/>
                            <group>
                                <group>
                                    <label for="hours_per_day" string="Avg"/>
                                    <div class="d-flex">
                                        <field name="hours_per_day" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/day</span>
                                    </div>
                                </group>
                                <group>
                                    <label for="hours_per_week" string="Total"/>
                                    <div class="d-flex">
                                        <field name="hours_per_week" nolabel="1" style="width: 6ch;" widget="float_time" readonly="1"/>
                                        <span class="ms-2">hours/week</span>
                                    </div>
                                </group>
                            </group>
                            <field name="attendance_ids_2nd_week" widget="section_one2many"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_resource_calendar_tree" model="ir.ui.view">
        <field name="name">resource.calendar.list</field>
        <field name="model">resource.calendar</field>
        <field name="arch" type="xml">
            <list string="Working Time">
                <field name="name" string="Working Time"/>
                <field name="hours_per_week" string="Schedule Total Time" optional="hide"/>
                <field name="work_time_rate"/>
                <field name="schedule_type"/>
                <field name="full_time_required_hours" widget="float_time" optional="hide"/>
                <field name="work_resources_count" string="# Work Resources" optional="hide"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
            </list>
        </field>
    </record>

    <record id="action_resource_calendar_form" model="ir.actions.act_window">
        <field name="name">Working Schedules</field>
        <field name="res_model">resource.calendar</field>
        <field name="view_mode">list,form</field>
        <field name="domain">['|', ('company_id', '=', False), ('company_id', 'in', allowed_company_ids)]</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="view_resource_calendar_search"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Define working hours and time table that could be scheduled to your project members
          </p>
        </field>
    </record>
</odoo>
