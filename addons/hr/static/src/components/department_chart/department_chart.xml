<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="hr.DepartmentChart">
        <div class="o_hr_department_chart w-100">
            <div class="o_horizontal_separator mb-3 text-uppercase fw-bolder small">Department Organization</div>
            <t t-if="state.hierarchy.self">
                <div class="o_hr_department_chart_parent">
                    <t t-set="dept" t-value="state.hierarchy.parent"/>
                    <t t-set="hideTree" t-value="true"/>
                    <t t-call="hr.DepartmentChart.Department"/>
                </div>

                <div t-att-class="state.hierarchy.parent?'ms-4':''">
                    <div class="o_hr_department_chart_self">
                        <t t-set="dept" t-value="state.hierarchy.self"/>
                        <t t-set="hideTree" t-value="!state.hierarchy.parent"/>
                        <t t-call="hr.DepartmentChart.Department"/>
                    </div>

                    <t t-set="hideTree" t-value="false"/>
                    <div class="o_hr_department_chart_children ms-4">
                        <t t-foreach="state.hierarchy.children" t-as="dept" t-key="dept.name">
                            <t t-call="hr.DepartmentChart.Department"/>
                        </t>
                    </div>
                </div>
            </t>
        </div>
    </t>

    <t t-name="hr.DepartmentChart.Department">
        <t t-if="dept">
            <div t-attf-class="#{hideTree?'':'o_treeEntry'}">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="department_name ms-1 mb-1">
                        <t t-esc="dept.name"/>
                    </span>
                    <button class="btn btn-secondary btn-sm rounded-pill ms-2 my-1"
                        t-on-click.prevent="() => this.openDepartmentEmployees(dept.id)">
                        <span class="badge top-0 px-0">
                            <t t-esc="dept.employees"/>
                        </span>
                    </button>
                </div>
            </div>
        </t>
    </t>
</templates>
