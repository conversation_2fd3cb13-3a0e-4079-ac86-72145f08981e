.o_wysiwyg_loader {
    @extend :disabled;
    pointer-events: none;
    min-height: 100px;
    color: transparent;
}
.o_wysiwyg_loading {
    @include o-position-absolute($top: 50%, $left: 50%);
    transform: translate(-50%, -50%)
}

@include media-breakpoint-down(md) {
    .o_we_shape:not(.o_shape_show_mobile) {
        display: none;
    }
}
.o_we_flip_x {
    transform: scaleX(-1);
}
.o_we_flip_y {
    transform: scaleY(-1);
}
.o_we_flip_x.o_we_flip_y {
    transform: scale(-1);
}

// GRID LAYOUT
.o_grid_mode {
    @include media-breakpoint-down(lg) {
        // No horizontal gap in mobile view.
        column-gap: 0px !important;
    }
    --grid-item-padding-y: 10px;
    --grid-item-padding-x: 10px;

    > * {
        padding: var(--grid-item-padding-y) var(--grid-item-padding-x) !important;

        @include media-breakpoint-down(lg) {
            // Force the horizontal padding to 15px in mobile view, to be
            // consistent with the normal mode.
            padding: var(--grid-item-padding-y) calc(0.5 * var(--gutter-x)) !important;

            &.o_grid_item_image {
                // If the horizontal padding is set to 0px, keep it as 0px in
                // mobile view, otherwise, force it to 15px.
                --mobile-grid-item-padding-x: clamp(0px, calc(var(--grid-item-padding-x) * 100000), calc(0.5 * var(--gutter-x)));
                padding: var(--grid-item-padding-y) var(--mobile-grid-item-padding-x) !important;

                > img {
                    max-width: 100%;
                }
            }
        }
    }
}

@include media-breakpoint-up(lg) {
    .o_grid_mode {
        display: grid !important;
        grid-auto-rows: 50px;
        grid-template-columns: repeat(12, 1fr);
        row-gap: 0px;
        column-gap: 0px;

        --gutter-x: 0px;

        // Adapt the horizontal margins of a direct row child of a grid item, to
        // make them compensate the grid item horizontal padding (to avoid an
        // overflow).
        .o_grid_item > .row,
        .o_grid_item > .o_text_columns > .row {
            --grid-inner-row-gutter-x: clamp(0px, 2 * var(--grid-item-padding-x), #{$grid-gutter-width});
            margin-left: calc(-0.5 * var(--grid-inner-row-gutter-x));
            margin-right: calc(-0.5 * var(--grid-inner-row-gutter-x));
        }

        > * {
            margin: 0 !important;
            width: 100%;
            min-width: 0;
        }
    }

    .container-fluid > .o_grid_mode {
        --gutter-x: #{$grid-gutter-width};
    }

    .o_extra_menu_items .o_grid_mode {
        display: flex !important;
        row-gap: 0px !important;
        column-gap: 0px !important;
    }

    .o_grid_item_image {
        img, .media_iframe_video {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
        }

        &.o_grid_item_image_contain img,
        img[data-shape] {
            object-fit: contain !important;
        }

        // Allowing "cover" for some hover effects.
        &:not(.o_grid_item_image_contain) img[data-shape$="geo_square"] {
            &:not(
                [data-hover-effect="dolly_zoom"],
                [data-hover-effect="outline"],
                [data-hover-effect="image_mirror_blur"],
            ) {
                object-fit: cover !important;
            }
        }

        // Needed when "a" is "display: block".
        > a {
            width: 100%;
            height: 100%;
        }
    }
}

// Override the default image selection color used in "web_editor". This
// prevents selected images from being covered with a blue filter in edit mode
// (Website, Email Marketing). This allows to see the correct final rendering
// of an image when it is edited.
body.editor_enable:not(.o_basic_theme) .odoo-editor-editable {
    img::selection {
        background-color: transparent !important;
    }
}

// Bootstrap is overridden to allow the use of CSS variables to configure border
// and radius. The main goal is to allow computation on the children layers (see
// %o-we-background-layer) whose radius would depend on both the parent border
// width and border radius.
// See BORDER_VARIABLES_RULES_EXTENSION
//
// --box-border-XXX-(radius|width) : editor Round Corners user input
// --box-border-(radius|width) : rounded-XXX classes
// --border-(radius|width) : theme (+ default bootstrap)

// BORDER WIDTH
%box-border-width-rules {
    // Setup shorthands and fallback: in any case, inline style (on the original
    // property or on the CSS variable) will dominate. Setting them up this way
    // allows to make reading/writing on those more precise (although not
    // fully: reading the main property will not detail the sub-properties).
    --box-border-width: var(--border-width);
    --box-border-top-width: var(--box-border-width);
    --box-border-right-width: var(--box-border-width);
    --box-border-bottom-width: var(--box-border-width);
    --box-border-left-width: var(--box-border-width);

    // TODO make it so builder options do not force all 4 values when the user
    // simply want to set a single value (and instead use the main CSS variable
    // in that case). Something to do with improving setStyle's understanding of
    // CSS_SHORTHANDS when it comes to CSS variables.

    border-width:
        var(--box-border-top-width)
        var(--box-border-right-width)
        var(--box-border-bottom-width)
        var(--box-border-left-width) !important;
}
// TODO this should probably be done for all bootstrap "border" classes like
// "rounded" classes below? At the moment, it was not done because builder
// options do not allow to set those but layouts could?
.border {
    @extend %box-border-width-rules;
}

// BORDER RADIUS
%box-border-radius-rules {
    // Setup shorthands and fallback: in any case, inline style (on the original
    // property or on the CSS variable) will dominate. Setting them up this way
    // allows to make reading/writing on those more precise (although not
    // fully: reading the main property will not detail the sub-properties).
    --box-border-radius: var(--border-radius);
    --box-border-top-left-radius: var(--box-border-radius);
    --box-border-top-right-radius: var(--box-border-radius);
    --box-border-bottom-right-radius: var(--box-border-radius);
    --box-border-bottom-left-radius: var(--box-border-radius);

    // TODO make it so builder options do not force all 4 values when the user
    // simply want to set a single value (and instead use the main CSS variable
    // in that case). Something to do with improving setStyle's understanding of
    // CSS_SHORTHANDS when it comes to CSS variables.

    border-radius:
        var(--box-border-top-left-radius)
        var(--box-border-top-right-radius)
        var(--box-border-bottom-right-radius)
        var(--box-border-bottom-left-radius) !important;
}
$utilities-radius-keys: ("rounded", "rounded-top", "rounded-end", "rounded-bottom", "rounded-start") !default;
@each $direction in $utilities-radius-keys {
    @each $size, $value in $rounded-sizes {
        $class-suffix: if($size == null, "", "-#{$size}");
        .#{$direction}#{$class-suffix} {
            @extend %box-border-radius-rules;
        }
    }
};
