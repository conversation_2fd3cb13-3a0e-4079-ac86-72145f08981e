<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

<t t-name="hr_org_chart.hr_org_chart_employee">
    <t t-set="is_self" t-value="employee.id == view_employee_id"/>

    <section t-if="employee_type == 'self'" t-attf-class="o_org_chart_entry_self_container #{managers.length &gt; 0 ? 'o_org_chart_has_managers' : ''}">
        <div t-attf-class="o_org_chart_entry o_org_chart_entry_#{employee_type} position-relative d-flex align-items-center overflow-visible #{managers.length &gt; 0 ? 'o_treeEntry' : ''}">
            <t t-call="hr_org_chart.hr_org_chart_employee_content">
                <t t-set="is_self" t-value="is_self"/>
            </t>
        </div>
    </section>

    <div t-else="" t-attf-class="o_org_chart_entry o_org_chart_entry_#{employee_type} o_treeEntry position-relative d-flex align-items-center overflow-visible">
        <t t-call="hr_org_chart.hr_org_chart_employee_content">
            <t t-set="is_self" t-value="is_self"/>
        </t>
    </div>
</t>

<t t-name="hr_org_chart.hr_org_chart_employee_informations">
    <div class="o_media_left align-self-start rounded z-1">
        <!-- NOTE: Use bg-images instead of img to get a clean and squared image -->
        <div
            class="o_media_object img-fluid d-block rounded"
            t-att-style="'background-image:url(\'/web/image/hr.employee.public/' + employee.id + '/avatar_1024/\')'"
            t-att-alt="employee.name"
        />
    </div>
    <div class="position-relative d-flex flex-column flex-grow-1 justify-content-center ps-2 lh-sm">
        <b class="o_media_heading fs-6 fw-bold" t-esc="employee.name"/>
        <small class="o_employee_job text-muted" t-attf-class="#{is_self ? 'fw-bold' : 'opacity-75 opacity-100-hover'}" t-esc="employee.job_name"/>
    </div>
</t>

<t t-name="hr_org_chart.hr_org_chart_employee_content">
    <a t-if="!is_self" t-att-href="employee.link" class="o_employee_redirect d-flex w-100 me-auto opacity-trigger-hover" t-att-data-employee-id="employee.id" t-on-click.prevent="() => this._onEmployeeRedirect(employee.id)">
        <t t-call="hr_org_chart.hr_org_chart_employee_informations">
            <t t-set="is_self" t-value="is_self"/>
        </t>
    </a>
    <div t-if="is_self" class="d-flex w-100 me-auto">
        <t t-call="hr_org_chart.hr_org_chart_employee_informations">
            <t t-set="is_self" t-value="is_self"/>
        </t>
    </div>
    <button t-if="employee.indirect_sub_count &gt; 0"
            class="btn btn-secondary btn-sm d-flex ms-2 rounded-pill"
            tabindex="0"
            t-att-data-emp-name="employee.name"
            t-att-data-emp-id="employee.id"
            t-att-data-emp-dir-subs="employee.direct_sub_count"
            t-att-data-emp-ind-subs="employee.indirect_sub_count"
            data-bs-trigger="focus"
            data-bs-toggle="popover"
            t-on-click="(event) => this._onOpenPopover(event, employee)">
        <span
            class="badge px-0"
            t-esc="employee.indirect_sub_count"
            />
    </button>
</t>

<t t-name="hr_org_chart.hr_org_chart">
    <!-- NOTE: Desidered behaviour:
            The maximun number of people is always 7 (including 'self'). Managers have priority over suburdinates
            Eg. 1 Manager + 1 self = show just 5 subordinates (if availables)
            Eg. 0 Manager + 1 self = show 6 subordinates (if available)

        -->
    <t t-set="emp_count" t-value="0"/>
    <div t-if='managers.length &gt; 0' class="o_org_chart_group_up position-relative">
        <div t-if='managers_more' class="o_org_chart_more pe-3">
            <a href="#" t-att-data-employee-id="managers[0].id" class="o_employee_more_managers d-block bg-100 px-2" t-on-click.prevent="() => this._onEmployeeMoreManager(managers[0].id)">
                <i class="fa fa-angle-double-up" role="img" aria-label="More managers" title="More managers"/>
            </a>
        </div>

        <t t-foreach="managers" t-as="employee" t-key="employee_index">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-call="hr_org_chart.hr_org_chart_employee">
                <t t-set="employee_type" t-value="'manager'"/>
            </t>
        </t>
    </div>

    <t t-if="children.length || managers.length" t-call="hr_org_chart.hr_org_chart_employee">
        <t t-set="employee_type" t-value="'self'"/>
        <t t-set="employee" t-value="self"/>
    </t>

    <t t-if="!children.length &amp;&amp; !managers.length">
        <p class="mb-3 text-muted fst-italic">Set a manager or reports to show in org chart.</p>
        <div class="o_org_chart_entry_self_container opacity-50">
            <div class="o_org_chart_entry o_org_chart_entry_self d-flex py-2 pe-none">
                <div class="o_media_left">
                    <div class="o_media_object placeholder rounded opacity-25"/>
                </div>
                <div class="position-relative d-flex flex-grow-1 align-items-center px-2">
                    <div class="d-flex flex-column w-100 opacity-25">
                        <b class="o_media_heading placeholder col-4 m-0"/>
                        <small class="placeholder placeholder-xs col-3 mt-1 text-muted"/>
                    </div>
                </div>
            </div>
        </div>
        <section class="o_org_chart_group_down o_org_chart_has_managers opacity-50">
            <div class="o_org_chart_entry o_treeEntry position-relative d-flex py-2 overflow-visible pe-none">
                <div class="o_media_left">
                    <div class="o_media_object o_employee_redirect placeholder rounded opacity-25"/>
                </div>
                <div class="d-flex flex-grow-1 align-items-center justify-content-between px-2">
                    <div class="d-flex flex-column w-100 lh-sm opacity-25">
                        <b class="o_media_heading placeholder col-3 m-0"/>
                        <small class="placeholder placeholder-xs col-7 mt-1 text-muted"/>
                    </div>
                </div>
            </div>
            <div class="o_org_chart_entry o_treeEntry position-relative d-flex py-2 overflow-visible pe-none">
                <div class="o_media_left">
                    <div class="o_media_object o_employee_redirect placeholder rounded opacity-25"/>
                </div>
                <div class="d-flex flex-grow-1 align-items-center px-2">
                    <div class="d-flex flex-column w-100 opacity-25">
                        <b class="o_media_heading placeholder col-5 m-0"/>
                        <small class="placeholder placeholder-xs col-4 mt-1 text-muted"/>
                    </div>
                </div>
            </div>
        </section>
    </t>

    <div t-if="children.length" t-attf-class="o_org_chart_group_down position-relative #{managers.length &gt; 0 ? 'o_org_chart_has_managers' : ''}">
        <t t-foreach="children" t-as="employee" t-key="employee_index">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-if="emp_count &lt; 20">
                <t t-call="hr_org_chart.hr_org_chart_employee">
                    <t t-set="employee_type" t-value="'sub'"/>
                </t>
            </t>
        </t>

        <t t-if="(children.length + managers.length) &gt; 19">
            <div class="o_org_chart_entry o_org_chart_more d-flex overflow-visible">
                <div class="o_media_left position-relative">
                    <a href="#"
                        t-att-data-employee-id="self.id"
                        t-att-data-employee-name="self.name"
                        class="o_org_chart_show_more o_employee_sub_redirect btn btn-link ps-2"
                        t-on-click.prevent="_onEmployeeSubRedirect">See All</a>
                </div>
            </div>
        </t>
    </div>
</t>

<t t-name="hr_org_chart.hr_orgchart_emp_popover">
    <div class="o_org_chart_popup" role="tooltip">
        <div class="tooltip-arrow"/>
        <h3 class="popover-header">
            <div class="d-flex align-items-center">
                <span class="o_media_object flex-shrink-0 rounded me-1" t-att-style='"background-image:url(\"/web/image/hr.employee.public/" + props.employee.id + "/avatar_1024/\")"'/>
                <b class="flex-grow-1 fw-medium"><t t-esc="props.employee.name"/></b>
                <a href="#" class="o_employee_redirect btn btn-link" t-att-data-employee-id="props.employee.id" t-on-click.prevent="() => this._onEmployeeRedirect(props.employee.id)"><i class="fa fa-user" role="img" aria-label='View employee' title="View employee"/></a>
            </div>
        </h3>
        <div class="popover-body">
            <table class="table table-sm table-borderless mb-0">
                <tbody>
                    <tr>
                        <td class="text-end"><b t-esc="props.employee.direct_sub_count"/></td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='direct'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                <b>Direct subordinates</b></a>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end">
                            <b t-esc="props.employee.indirect_sub_count - props.employee.direct_sub_count"/>
                        </td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='indirect'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                Indirect subordinates</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end"><b t-esc="props.employee.indirect_sub_count"/></td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='total'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                Total</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</t>

</templates>
