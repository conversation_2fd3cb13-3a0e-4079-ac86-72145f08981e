{"version": "18.4.14", "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 26, "rowNumber": 99, "rows": {}, "cols": {"0": {"size": 269}}, "merges": ["A15:E16", "A34:E35"], "cells": {"A15": "[Payslip Lines](odoo://ir_menu_xml_id/hr_payroll.menu_hr_payslip_line_action_report)", "A34": "[Work Entries Analysis](odoo://ir_menu_xml_id/hr_payroll.menu_hr_work_entry_report)", "A36": "=_t(\"Work type\")", "A37": "=PIVOT.HEADER(7,\"work_entry_type_id\",1)", "A38": "=PIVOT.HEADER(7,\"work_entry_type_id\",3)", "A39": "=PIVOT.HEADER(7,\"work_entry_type_id\",6)", "A40": "=PIVOT.HEADER(7,\"work_entry_type_id\",7)", "A41": "=PIVOT.HEADER(7,\"work_entry_type_id\",8)", "A42": "=PIVOT.HEADER(7)", "B36": "=PIVOT.HEADER(7,\"measure\",\"number_of_days:sum\")", "B37": "=PIVOT.VALUE(7,\"number_of_days:sum\",\"work_entry_type_id\",1)", "B38": "=PIVOT.VALUE(7,\"number_of_days:sum\",\"work_entry_type_id\",3)", "B39": "=PIVOT.VALUE(7,\"number_of_days:sum\",\"work_entry_type_id\",6)", "B40": "=PIVOT.VALUE(7,\"number_of_days:sum\",\"work_entry_type_id\",7)", "B41": "=PIVOT.VALUE(7,\"number_of_days:sum\",\"work_entry_type_id\",8)", "B42": "=PIVOT.VALUE(7,\"number_of_days:sum\")"}, "styles": {"A15:E16": 1, "A34:E35": 2, "A36:B36": 3}, "formats": {}, "borders": {"A36:B36": 1}, "conditionalFormats": [{"id": "36e4304b-6c7e", "rule": {"type": "DataBarRule", "color": 14281427, "rangeValues": "B37:B41"}, "ranges": ["A37:A44"]}], "dataValidationRules": [], "figures": [{"id": "b404ea45-e061", "col": 3, "row": 1, "offset": {"x": 76, "y": 4}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Total Net Wage", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!C6", "keyValue": "Data!B6", "humanize": false}}, {"id": "9ef81369-f423", "col": 6, "row": 1, "offset": {"x": 44, "y": 4}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Total Basic Wage", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!C7", "keyValue": "Data!B7", "humanize": false}}, {"id": "b551d5f4-b068", "col": 0, "row": 1, "offset": {"x": 22, "y": 4}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Avg Net Wage", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!C3", "keyValue": "Data!B3", "humanize": false}}, {"id": "742e78cf-e36f", "col": 1, "row": 1, "offset": {"x": 6, "y": 4}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Avg Basic Wage", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "Data!C4", "keyValue": "Data!B4", "humanize": false}}, {"id": "5e2a5991-057b", "col": 0, "row": 6, "offset": {"x": 22, "y": 15}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Avg Hours/Day", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C11", "keyValue": "Data!B11", "humanize": false}}, {"id": "fd349ef1-755a", "col": 1, "row": 6, "offset": {"x": 6, "y": 15}, "width": 232, "height": 113, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "text", "title": {"text": "FTE", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!B12", "baselineDescr": {"text": "Employees"}, "keyValue": "Data!B13", "humanize": false}}, {"id": "7f8c2bd8-a13f", "col": 0, "row": 17, "offset": {"x": 11, "y": 9}, "width": 850, "height": 335, "tag": "chart", "data": {"type": "line", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "Data!B18:E18", "label": "BASIC"}, {"dataRange": "Data!B19:E19", "label": "GROSS"}, {"dataRange": "Data!B20:E20", "label": "NET"}], "legendPosition": "top", "labelRange": "Data!B16:E16", "title": {}, "labelsAsText": false, "stacked": false, "aggregated": false, "cumulative": false, "fillArea": false, "showValues": false, "hideDataMarkers": false}}], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "c7e164d0-c8b7", "name": "Data", "colNumber": 26, "rowNumber": 96, "rows": {}, "cols": {"0": {"size": 197}}, "merges": [], "cells": {"A2": "=_t(\"#Payslips\")", "A3": "=_t(\"Average Net Wage\")", "A4": "=_t(\"Average Basic Wage\")", "A5": "=_t(\"Average Gross Wage\")", "A6": "=_t(\"Total Net Wage\")", "A7": "=_t(\"Total Basic Wage\")", "A8": "=_t(\"Total Gross Wage\")", "A9": "=_t(\"Total Hours of Work\")", "A10": "=_t(\"Total Days of Work\")", "A11": "=_t(\"Average Hours per Days of Work\")", "A12": "=_t(\"Employees\")", "A13": "=_t(\"FTE\")", "A18": "=PIVOT.HEADER(1,\"code\",\"BASIC\")", "A19": "=PIVOT.HEADER(1,\"code\",\"GROSS\")", "A20": "=PIVOT.HEADER(1,\"code\",\"NET\")", "A21": "=PIVOT.HEADER(1)", "B1": "=_t(\"Current\")", "B3": "='Payslip Line (Pivot #1)'!P5", "B4": "='Payslip Line (Pivot #1)'!P3", "B5": "='Payslip Line (Pivot #1)'!P4", "B6": "='Payslip Line (Pivot #1)'!N5", "B7": "='Payslip Line (Pivot #1)'!N3", "B8": "='Payslip Line (Pivot #1)'!N4", "B9": "='Payslip Worked Days (Pivot #4)'!E3", "B10": "='Payslip Worked Days (Pivot #4)'!D3", "B11": "=B9/B10", "B12": "='Employee Contract (Pivot #6)'!C3", "B13": "='Employee Contract (Pivot #6)'!B3", "B16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"3/2024\")", "B17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"3/2024\",\"measure\",\"amount:sum\")", "B18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"3/2024\")", "B19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"3/2024\")", "B20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"3/2024\")", "B21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"3/2024\")", "C1": "=_t(\"Previous\")", "C3": "='Payslip Line (last year) (Pivot #3)'!D5", "C4": "='Payslip Line (last year) (Pivot #3)'!D3", "C5": "='Payslip Line (last year) (Pivot #3)'!D4", "C6": "='Payslip Line (last year) (Pivot #3)'!B5", "C7": "='Payslip Line (last year) (Pivot #3)'!B3", "C8": "='Payslip Line (last year) (Pivot #3)'!B4", "C9": "='Payslip Worked Days (copy) (Pivot #8)'!E5", "C10": "='Payslip Worked Days (copy) (Pivot #8)'!D5", "C11": "=C9/C10", "C16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"4/2024\")", "C17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"4/2024\",\"measure\",\"amount:sum\")", "C18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"4/2024\")", "C19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"4/2024\")", "C20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"4/2024\")", "C21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"4/2024\")", "D16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"1/2025\")", "D17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"1/2025\",\"measure\",\"amount:sum\")", "D18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"1/2025\")", "D19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"1/2025\")", "D20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"1/2025\")", "D21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"1/2025\")", "E16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"2/2025\")", "E17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"2/2025\",\"measure\",\"amount:sum\")", "E18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"2/2025\")", "E19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"2/2025\")", "E20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"2/2025\")", "E21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"2/2025\")", "F16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"3/2025\")", "F17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"3/2025\",\"measure\",\"amount:sum\")", "F18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"3/2025\")", "F19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"3/2025\")", "F20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"3/2025\")", "F21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"3/2025\")", "G16": "=PIVOT.HEADER(1,\"date_from:quarter\",\"4/2025\")", "G17": "=PIVOT.HEADER(1,\"date_from:quarter\",\"4/2025\",\"measure\",\"amount:sum\")", "G18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"4/2025\")", "G19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"4/2025\")", "G20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"4/2025\")", "G21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"4/2025\")", "L18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"1/2027\")", "L19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"1/2027\")", "L20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"1/2027\")", "L21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"1/2027\")", "M18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"2/2027\")", "M19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"2/2027\")", "M20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"2/2027\")", "M21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"2/2027\")", "N18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"3/2027\")", "N19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"3/2027\")", "N20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"3/2027\")", "N21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"3/2027\")", "O18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"4/2027\")", "O19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"4/2027\")", "O20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"4/2027\")", "O21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"4/2027\")", "P18": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"BASIC\",\"date_from:quarter\",\"1/2028\")", "P19": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"GROSS\",\"date_from:quarter\",\"1/2028\")", "P20": "=PIVOT.VALUE(1,\"amount:sum\",\"code\",\"NET\",\"date_from:quarter\",\"1/2028\")", "P21": "=PIVOT.VALUE(1,\"amount:sum\",\"date_from:quarter\",\"1/2028\")"}, "styles": {"A2:A15": 4, "B1:E1": 4}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A17", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "5e21e167-3136", "name": "Payslip Line (Pivot #1)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 79}}, "merges": [], "cells": {"A1": "=PIVOT(\"1\")"}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "579ea217-7ea1", "name": "Payslip Line (last year) (Pivot #3)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 146}}, "merges": [], "cells": {"A1": "=PIVOT(3)"}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "8c304543-a607", "name": "Payslip Worked Days (Pivot #4)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 130}, "3": {"size": 101}, "4": {"size": 108}}, "merges": [], "cells": {"A1": "=PIVOT(\"4\")"}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": false, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "44e7c162-8bf0", "name": "Payslip Worked Days (copy) (Pivot #8)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": "=PIVOT(8)"}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": false, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "0fa9d14a-c1cf", "name": "Employee Contract (Pivot #6)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": "=PIVOT(\"6\")"}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": false, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "5bb29560-76c1", "name": "Work Entries Analysis Report (Pivot #7)", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 251}}, "merges": [], "cells": {"A1": "=PIVOT(\"7\")"}, "styles": {"A10:B10": 3, "A11:B20": 5}, "formats": {}, "borders": {"A10:B10": 1, "A11:B11": 2, "A12:B20": 3}, "conditionalFormats": [{"id": "2d8209f3-cf92", "rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "B31:B40"}, "ranges": ["A11:A20"]}], "dataValidationRules": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": false}}], "areGridLinesVisible": true, "isVisible": false, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}], "styles": {"1": {"verticalAlign": "middle", "bold": true, "fontSize": 16}, "2": {"fontSize": 16, "bold": true}, "3": {"textColor": "#434343", "fontSize": 11, "bold": true}, "4": {"bold": true}, "5": {"textColor": "#434343", "verticalAlign": "middle"}}, "formats": {}, "borders": {"1": {"top": {"style": "thin", "color": "#CCCCCC"}}, "2": {"bottom": {"style": "thick", "color": "#FFFFFF"}}, "3": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}}}, "revisionId": "9ece465a-5d52-467f-8933-865a356aa5c3", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"83dcdffb-7412": {"type": "ODOO", "model": "hr.payslip.line", "name": "Payslip Line", "domain": [["code", "in", ["BASIC", "NET", "GROSS"]]], "context": {}, "columns": [{"fieldName": "date_from", "order": "asc", "granularity": "quarter"}], "rows": [{"fieldName": "code", "order": "asc"}], "measures": [{"id": "amount:sum", "fieldName": "amount", "aggregator": "sum", "isHidden": false}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}, {"id": "amount:avg", "fieldName": "amount", "aggregator": "avg"}], "formulaId": "1", "fieldMatching": {"dff0119f-b29a": {"chain": "slip_id.date_from", "type": "date", "offset": 0}}}, "eb6f1fe4-6820": {"type": "ODOO", "model": "hr.payslip.line", "name": "Payslip Line (Last Year)", "domain": [["code", "in", ["BASIC", "NET", "GROSS"]]], "context": {}, "columns": [], "rows": [{"fieldName": "code", "order": "asc"}], "measures": [{"id": "amount:sum", "fieldName": "amount", "aggregator": "sum", "isHidden": false}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}, {"id": "amount:avg", "fieldName": "amount", "aggregator": "avg"}], "formulaId": "3", "fieldMatching": {"dff0119f-b29a": {"chain": "slip_id.date_from", "type": "date", "offset": 1}}}, "0dd5eb86-fbc2": {"type": "ODOO", "model": "hr.payslip.worked_days", "name": "Payslip Worked Days", "domain": [], "context": {}, "columns": [], "rows": [{"fieldName": "work_entry_type_id", "order": "asc"}], "measures": [{"id": "amount:sum", "fieldName": "amount", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}, {"id": "number_of_days:sum", "fieldName": "number_of_days", "aggregator": "sum"}, {"id": "number_of_hours:sum", "fieldName": "number_of_hours", "aggregator": "sum"}], "formulaId": "4", "fieldMatching": {"dff0119f-b29a": {"chain": "payslip_id.date_from", "type": "date", "offset": 0}}}, "71175e0f-c981": {"type": "ODOO", "model": "hr.version", "name": "Employee Contract", "domain": [], "context": {}, "columns": [], "rows": [], "measures": [{"id": "work_time_rate:sum", "fieldName": "work_time_rate", "aggregator": "sum"}, {"id": "employee_id:count_distinct", "fieldName": "employee_id", "aggregator": "count_distinct"}], "formulaId": "6", "fieldMatching": {"dff0119f-b29a": {}}}, "cc7bea4b-702e": {"type": "ODOO", "model": "hr.work.entry.report", "name": "Work Entries Analysis by Work Entry Type", "domain": [], "context": {}, "columns": [], "rows": [{"fieldName": "work_entry_type_id"}], "measures": [{"id": "number_of_days:max", "fieldName": "number_of_days", "aggregator": "max"}, {"id": "__count:max", "fieldName": "__count", "aggregator": "max"}, {"id": "number_of_days:sum", "fieldName": "number_of_days", "aggregator": "sum"}], "formulaId": "7", "fieldMatching": {"dff0119f-b29a": {}}}, "09919042-b26f": {"type": "ODOO", "model": "hr.payslip.worked_days", "name": "Payslip Worked Days (last Year)", "domain": [], "context": {}, "columns": [], "rows": [{"fieldName": "work_entry_type_id", "order": "asc"}], "measures": [{"id": "amount:sum", "fieldName": "amount", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}, {"id": "number_of_days:sum", "fieldName": "number_of_days", "aggregator": "sum"}, {"id": "number_of_hours:sum", "fieldName": "number_of_hours", "aggregator": "sum"}], "formulaId": "8", "fieldMatching": {"dff0119f-b29a": {"chain": "payslip_id.date_from", "type": "date", "offset": 1}}}}, "pivotNextId": 9, "customTableStyles": {}, "globalFilters": [{"id": "dff0119f-b29a", "label": "last 12 month", "type": "date", "defaultValue": "last_12_months"}], "lists": {}, "listNextId": 1, "chartOdooMenusReferences": {}}