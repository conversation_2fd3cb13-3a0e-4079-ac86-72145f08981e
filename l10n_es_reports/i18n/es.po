# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-20 14:49+0000\n"
"PO-Revision-Date: 2023-03-20 14:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid ""
"        This field corresponds to the activity to which the entry refers in 7 alphanumeric characters.\n"
"\n"
"        For example, in the operations of a hardware store, 'A036533' will be entered, which indicates an operation        carried out by a business activity of a commercial nature subject to the IAE for 'retail trade in household        items, hardware, ornaments.'"
msgstr ""
"        Este campo corresponde a la actividad a la que se refiere la entrada en 7 caracteres alfanuméricos.\n"
"\n"
"        Por ejemplo, en las operaciones de una ferretería, se introducirá 'A036533' lo que indica una operación        realizada por una actividad empresarial de carácter comercial sujeta al IAE de 'comercio al por menor de artículos para el hogar, ferretería, adornos.'"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "A - Acquisition"
msgstr "A - Adquisición"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.invoice_form_inherit
msgid "AEAT data"
msgstr "Datos de la AEAT"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Code"
msgstr "Código de cuenta"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Name"
msgstr "Nombre de cuenta"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_report
msgid "Accounting Report"
msgstr "Informe contable"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_return
msgid "Accounting Return"
msgstr "Declaración de impuestos"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_return_type
msgid "Accounting Return Type"
msgstr "Tipo de declaración de impuestos"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__a
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_key__a
msgid "Adquisiciones de bienes y servicios"
msgstr "Adquisiciones de bienes y servicios"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__local_negocio
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__local_negocio
msgid "Arrendamiento Local Negocio"
msgstr "Arrendamiento Local Negocio"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid "Available for Mod347"
msgstr "Disponible para Mod347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid "Available for Mod349"
msgstr "Disponible para Mod349"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "BOE"
msgstr "BOE"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_export_wizard
msgid "BOE Export Wizard"
msgstr "Asistente de exportación BOE"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111_export_wizard
msgid "BOE Export Wizard for (mod111)"
msgstr "Asistente de exportación BOE para (mod111)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard
msgid "BOE Export Wizard for (mod111, mod115 & 303)"
msgstr "Asistente de exportación BOE para (mod111, mod115 y 303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod115_export_wizard
msgid "BOE Export Wizard for (mod115)"
msgstr "Asistente de exportación BOE para (mod115)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod130_export_wizard
msgid "BOE Export Wizard for (mod130)"
msgstr "Asistente para la exportación del BOE para (mod130)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod303_export_wizard
msgid "BOE Export Wizard for (mod303)"
msgstr "Asistente de exportación BOE para (mod303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347and349_export_wizard
msgid "BOE Export Wizard for (mod347 & mod349)"
msgstr "Asistente de exportación BOE para (mod347 y mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347_export_wizard
msgid "BOE Export Wizard for (mod347)"
msgstr "Asistente de exportación BOE para (mod347)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod349_export_wizard
msgid "BOE Export Wizard for (mod349)"
msgstr "Asistente de exportación BOE para (mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod390_export_wizard
msgid "BOE Export Wizard for (mod390)"
msgstr "Asistente de exportación BOE para (mod390)"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis_beneficiary
msgid "Beneficiary of the special cash regime"
msgstr "Beneficiario del régimen especial de criterio de caja"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__c
msgid "C - Compensation Request"
msgstr "C - Solicitud de indemnización"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "C - Replacements of goods"
msgstr "C - Sustitución de bienes"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr "Llamada al asistente de exportación"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod130_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Cannot generate a BOE file for two different years"
msgstr "No se puede generar un archivo BOE para dos años diferentes"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid "Cash Basis Data"
msgstr "Datos de criterio de caja"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_partner_id
msgid "Company Partner"
msgstr "Contacto de la compañía"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid "Complementary Declaration"
msgstr "Declaración complementaria"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Contact person"
msgstr "Persona de contacto"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Contact phone number"
msgstr "Número de teléfono de contacto"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Corrective Self-Assessment"
msgstr "Autoliquidación rectificativa"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Credit"
msgstr "Crédito"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Currency %s, used for a threshold in this report, is either nonexistent or "
"inactive. Please create or activate it."
msgstr ""
"La moneda %s, utilizada para un umbral en este informe, no existe o está "
"inactiva. Créela o actívela."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_id
msgid "Current Company"
msgstr "Compañía actual"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__d
msgid "D - Return"
msgstr "D - Devolución"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "D - Returns of goods previously sent from the TAI"
msgstr "D - Devoluciones de bienes previamente enviados desde el TAI"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Date"
msgstr "Fecha"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Debit"
msgstr "Débito"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type
msgid "Declaration Type"
msgstr "Tipo de declaración"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Defines the category into which this invoice falls for mod 347 report."
msgstr ""
"Define la categoría en la que se incluye esta factura para el informe mod "
"347"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Defines the category into which this invoice falls for mod 349 report"
msgstr ""
"Define la categoría en la que se incluye esta factura para el informe mod "
"349"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Description"
msgstr "Descripción"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid "Direct Debit Account"
msgstr "Cuenta de domiciliación bancaria"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_general_ledger_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_generic_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_report__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return_type__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_libros_registro_export_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod111_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod115_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod130_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod303_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod347_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod349_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod390_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Document"
msgstr "Documento"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "E - Supply"
msgstr "E - Suministro"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__b
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_key__b
msgid "Entregas de bienes y prestaciones de servicios"
msgstr "Entregas de bienes y prestaciones de servicios"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Entry"
msgstr "Asiento"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid "Exempted From Mod 390 Available"
msgstr "Exento del Mod 390 disponible"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390
msgid "Exempted From Modelo 390"
msgstr "Exento del Modelo 390"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Formato de exportación para informes contables"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Asistente de exportación para informes contables"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__g
msgid "G - Income to enter on CCT"
msgstr "G - Ingresos a ingresar en el sistema CCT"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__g
msgid "G - Tributaria Current Account - Income"
msgstr "G - Cuenta corriente tributaria - Ingresos"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "General Ledger - %(date_from)s_%(date_to)s"
msgstr "Libro mayor - %(date_from)s_%(date_to)s"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "Gestor personalizado del libro mayor"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod130_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Generate BOE"
msgstr "Generar BOE"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr "Gestor personalizado de los informes genéricos de impuestos"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
#, python-format
msgid "Go to Company"
msgstr "Ir a compañía"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__group_number
msgid "Group of entities - Group Number"
msgstr "Grupo de entidades - Número del grupo"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "H - Supply without taxes delivered by a legal representative"
msgstr "H - Suministro sin impuestos entregado por un representante legal"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__i
msgid "I - Income"
msgstr "I - Ingreso"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "I - Services acquisition"
msgstr "I - Adquisición de servicios"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid "IAE Group or Heading"
msgstr "Grupo o epígrafe IAE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_general_ledger_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_generic_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_report__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return_type__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_libros_registro_export_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod111_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod115_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod130_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod303_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod347_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod349_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod390_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__id
msgid "ID"
msgstr "ID"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__monthly_return
msgid "In Monthly Return Register"
msgstr "En el registro de devolución mensual"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__insurance
msgid "Insurance operation"
msgstr "Operación de seguro"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Judicial person"
msgstr "Persona jurídica"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_id
msgid "L10N Es Reports Boe Wizard"
msgstr "L10N Es Asistente de informes BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_model
msgid "L10N Es Reports Boe Wizard Model"
msgstr "L10N Es Modelo de asistente de informes BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Libro Diario XLSX"
msgstr "Libro diario XLSX"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Line"
msgstr "Línea"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "M - Supply without taxes"
msgstr "M - Suministro sin impuestos"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid ""
"Manual entries containing the amounts perceived for the partners with cash "
"basis criterion during this year. Leave empty for partners for which this "
"criterion does not apply."
msgstr ""
"Entradas manuales que contienen los importes percibidos por los contactos "
"con criterio de caja durante este año. Déjelo vacío para los contactos a los"
" que no se aplica este criterio."

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_mod347_manual_partner_data
msgid "Manually Entered Data for Mod 347 Report"
msgstr "Datos introducidos manualmente para el informe Mod 347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__monthly_return
msgid "Monthly return record in some period of the fiscal year"
msgstr "Registro de devolución mensual en algún periodo del ejercicio fiscal"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__n
msgid "N - No Activity / Zero Result"
msgstr "N - Sin actividad / Resultado cero"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__n
msgid "N - To return"
msgstr "N - A devolver"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Name of the contact person fot this BOE file's submission"
msgstr ""
"Nombre de la persona de contacto para la presentación de este archivo BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__physical_person_name
msgid "Natural Person - Name"
msgstr "Persona natural - Nombre"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "No TIN set for partner %(name)s (id %(id)d). Please define one."
msgstr ""
"No se ha definido un NIF para el partner %(name)s (id %(id)d). Defina uno."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid ""
"Number of the previous report, corrected or replaced by this one, if any"
msgstr ""
"Número del informe anterior, corregido o sustituido por éste, en su caso"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
msgid "Number of the report previously submitted"
msgstr "Número del informe presentado anteriormente"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__seguros
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__seguros
msgid "Operaciones de Seguros"
msgstr "Operaciones de seguros"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_class
msgid "Operation Class"
msgstr "Clase de operación"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_key
msgid "Operation Key"
msgstr "Clave de operación"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this BOE wizard, "
"if there is one."
msgstr ""
"Campo opcional que contiene el asistente de exortación de informes que llama"
" a este asistente BOE, si existe"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__otras
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__otras
msgid "Otras operaciones"
msgstr "Otras operaciones"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__parent_wizard_id
msgid "Parent Wizard"
msgstr "Asistente principal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_in_tax_unit
msgid "Part of a tax unit"
msgstr "Parte de una unidad tributaria"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is located in Spain but does not have any "
"province. Please set one."
msgstr ""
"El contacto %(name)s (id %(id)d) se encuentra en España, pero no tiene "
"ninguna provincia. Establezca una."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is not associated to any Spanish province, and "
"should hence have a country code. For this, fill in its 'country' field."
msgstr ""
"El contacto %(name)s (id %(id)d) no está asociado a ninguna provincia "
"española y debería tener un código de país. Para ello, rellene el campo "
"'país'."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__perceived_amount
msgid "Perceived Amount"
msgstr "Importe percibido"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Phone number where to join the contact person"
msgstr "Número de teléfono de la persona de contacto"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
#, python-format
msgid "Please configure the \"IAE Group or Heading\" of your company."
msgstr "configure el \"Grupo o epígrafe IAE\" de su compañía."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first assign a BIC number to the bank related to this account."
msgstr ""
"Por favor, asigne primero un número BIC al banco relacionado con esta "
"cuenta."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first set the TIN of your company."
msgstr "Introduzca primero el NIF de su compañía"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "Please select a Spanish invoice type for this invoice."
msgstr "Seleccione un tipo de factura española para esta factura."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please select an IBAN account."
msgstr "Seleccione una cuenta IBAN."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid "Previous Report Number"
msgstr "Número del informe anterior"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__previous_decl_number
msgid "Previous declaration no."
msgstr "N.° de la declaración anterior"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_activity
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Principal activity"
msgstr "Actividad principal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_code_activity
msgid "Principal activity - Activity Code"
msgstr "Actividad principal - Código de actividad"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_iae_epigrafe
msgid "Principal activity - Epígrafe"
msgstr "Actividad principal - Epígrafe"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Print BOE"
msgstr "Imprimir BOE"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "R - Transfers of goods made under consignment sales agreements"
msgstr ""
"R - Transferencias de bienes efectuadas en el marco de acuerdos de bienes en"
" consigna"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Reading a non-Spanish TIN as a Spanish TIN."
msgstr "Lectura de un NIF no español como un NIF español"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Refund Invoice %s was created without a link to the original invoice that "
"was credited, while we need that information for this report. "
msgstr ""
"La factura de reembolso %s se creó sin un enlace a la factura original que "
"se abonó, mientras que necesitamos esa información para este informe."

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__regular
msgid "Regular operation"
msgstr "Operación regular"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__report_id
msgid "Report"
msgstr "Informe"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_nif
msgid "Representative - NIF"
msgstr "Representante - NIF"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_name
msgid "Representative - Name and Surname"
msgstr "Representante - Nombre y apellidos"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_notary
msgid "Representative - Notary"
msgstr "Representante - Notario"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_procuration_date
msgid "Representative - Power of Attorney Date"
msgstr "Representante - Fecha del poder notarial"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "S - Services sale"
msgstr "S - Venta de servicios"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_libros_registro_export_handler
msgid "Spanish Libros Registro de IVA"
msgstr "Libros registro de IVA de España"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_tax_report_handler
msgid "Spanish Tax Report Custom Handler"
msgstr "Gestor personalizado de los informes de impuestos españoles"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod111_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod111)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod111)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod115_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod115)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod115)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod130_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod130)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod130)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod303_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod303)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod347_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod347)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod347)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod349_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod349)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod390_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod390)"
msgstr "Gestor personalizado de los informes de impuestos españoles (Mod390)"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_regime_applicable_163
msgid "Special Regime Art. 163 is applicable"
msgstr "Régimen especial: El Art. 163 es aplicable"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Special Regimen"
msgstr "Régimen especial"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis
msgid "Special cash basis regime"
msgstr "Régimen especial del criterio de caja"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Substitute declaration"
msgstr "Sustituir declaración"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Substitutive Declaration"
msgstr "Declaración sustitutiva"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_decl_by_rectif_of_quotas
msgid "Substitutive declaration for correction of quotas"
msgstr "Declaración sustitutiva para correción de cuotas"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_declaration
msgid "Substitutive declaration?"
msgstr "¿Declaración sustitutiva?"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "T - Triangular Operation"
msgstr "T - Operación triangular"

#. module: l10n_es_reports
#: model:account.return.type,name:l10n_es_reports.es_mod303_tax_return_type
msgid "Tax Return (Mod 303) (ES)"
msgstr "Declaración de impuestos (Mod 303) (ES)"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_id
msgid "Taxpayer ID"
msgstr "Identificación fiscal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_first_name
msgid "Taxpayer first name"
msgstr "Nombre del contribuyente"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_last_name
msgid "Taxpayer last name"
msgstr "Apellido del contribuyente"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid ""
"Technical field used to only make exempted_from_mod_390 avilable in the last"
" period (12 or 4T)"
msgstr ""
"Campo técnico utilizado para que exempted_from_mod_390 sólo esté disponible "
"en el último periodo (12 o 4T)"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid ""
"The IBAN account number to use for direct debit. Leave blank if you don't "
"use direct debit."
msgstr ""
"El número de cuenta IBAN a utilizar para la domiciliación bancaria. Déjelo "
"vacío si no utiliza la domiciliación bancaria."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "This report export is only available for ES companies."
msgstr ""
"Esta exportación de informes sólo está disponible para las empresas ES."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid "Trimester monthly report"
msgstr "Informe mensual del trimestre"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid ""
"True if and only if the invoice MIGHT need to be reported on mod 347, i.e. "
"it concerns an operation from a Spanish headquarter."
msgstr ""
"True si y sólo si la factura PODRÍA tener que ser declarada en el mod 347, "
"es decir, si se refiere a una operación desde una sede española."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid ""
"True if and only if the invoice must be reported on mod 349 report, i.e. it "
"concerns an intracommunitary operation."
msgstr ""
"True si y sólo si la factura tiene que ser declarada en el mod 349, es "
"decir, si se trata de una operación intracomunitaria."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Type for mod 347"
msgstr "Tipo para mod 347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Type for mod 349"
msgstr "Tipo para mod 349"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__u
msgid "U - Direct Debit of the Income in CCC"
msgstr "U - Domiciliación de los ingresos en CCC"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__u
msgid "U - Direct debit"
msgstr "U - Domiciliación bancaria"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "Unable to find matching surcharge tax in %s"
msgstr ""
"No se ha podido encontrar el impuesto de recargo correspondiente en %s"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__using_sii
msgid "Using SII Voluntarily"
msgstr "Uso voluntario del SII"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__v
msgid "V - Tributaria Current Account - Return"
msgstr "V - Cuenta corriente tributaria - Devolución"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "VAT Record Books (XLSX)"
msgstr "Libros de registro de IVA (XLSX)"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid ""
"Whether or not this BOE file corresponds to a complementary declaration"
msgstr "Si este archivo BOE corresponde o no a una declaración complementaria"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Whether or not this BOE file corresponds to a substitutive declaration"
msgstr "Si este archivo BOE corresponde o no a una declaración sustitutiva"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a complementary declaration."
msgstr "Si este archivo BOE es o no una declaración complementaria"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a corrective self-assessment."
msgstr "Si este archivo BOE es o no una autoliquidación rectificativa."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid ""
"Whether or not this BOE file must be generated with the data of the first "
"two months of the trimester (in case its total amount of operation is above "
"the threshold fixed by the law)"
msgstr ""
"Si este archivo BOE debe o no generarse con los datos de los dos primeros "
"meses del trimestre (en caso de que su importe total de operación supere el "
"umbral fijado por la ley)."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of a year."
msgstr ""
"Fechas de informe erróneas para la generación de BOE: seleccione un periodo "
"de un año"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of one month "
"or a trimester."
msgstr ""
"Fechas de informe erróneas para la generación del BOE: seleccione un periodo"
" de un mes o un trimestre."

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__x
msgid "X - Return by Transfer Abroad (Only for Periods 3T, 4T and 07 to 12)"
msgstr ""
"X - Devolución por traslado al extranjero (sólo para los periodos 3T, 4T y "
"07 a 12)"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"You cannot generate a BOE file for the first two months of a trimester if "
"only one month is selected!"
msgstr ""
"No se puede generar un archivo BOE para los dos primeros meses de un "
"trimestre si sólo ha seleccionado un mes."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Your date range does not cover entire months, please use a start and end "
"date matching respectively the first and last day of a month."
msgstr ""
"Su intervalo de fechas no cubre meses enteros, por favor utilice una fecha "
"de inicio y finalización que coincidan respectivamente con el primer y "
"último día de un mes."
