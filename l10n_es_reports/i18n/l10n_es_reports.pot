# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~18.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-04 12:42+0000\n"
"PO-Revision-Date: 2025-06-04 12:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid ""
"        This field corresponds to the activity to which the entry refers in 7 alphanumeric characters.\n"
"\n"
"        For example, in the operations of a hardware store, 'A036533' will be entered, which indicates an operation        carried out by a business activity of a commercial nature subject to the IAE for 'retail trade in household        items, hardware, ornaments.'"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "A - Acquisition"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.invoice_form_inherit
msgid "AEAT data"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Code"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Name"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_return
msgid "Accounting Return"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_return_type
msgid "Accounting Return Type"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__a
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_key__a
msgid "Adquisiciones de bienes y servicios"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__local_negocio
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__local_negocio
msgid "Arrendamiento Local Negocio"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid "Available for Mod347"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid "Available for Mod349"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "BOE"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_export_wizard
msgid "BOE Export Wizard"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111_export_wizard
msgid "BOE Export Wizard for (mod111)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard
msgid "BOE Export Wizard for (mod111, mod115 & 303)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod115_export_wizard
msgid "BOE Export Wizard for (mod115)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod130_export_wizard
msgid "BOE Export Wizard for (mod130)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod303_export_wizard
msgid "BOE Export Wizard for (mod303)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347and349_export_wizard
msgid "BOE Export Wizard for (mod347 & mod349)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347_export_wizard
msgid "BOE Export Wizard for (mod347)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod349_export_wizard
msgid "BOE Export Wizard for (mod349)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod390_export_wizard
msgid "BOE Export Wizard for (mod390)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis_beneficiary
msgid "Beneficiary of the special cash regime"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__c
msgid "C - Compensation Request"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "C - Replacements of goods"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod130_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Cancel"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Cannot generate a BOE file for two different years"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid "Cash Basis Data"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_partner_id
msgid "Company Partner"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid "Complementary Declaration"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Contact person"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Contact phone number"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Corrective Self-Assessment"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_date
msgid "Created on"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Credit"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Currency %s, used for a threshold in this report, is either nonexistent or "
"inactive. Please create or activate it."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_id
msgid "Current Company"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__d
msgid "D - Return"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "D - Returns of goods previously sent from the TAI"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Date"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Debit"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type
msgid "Declaration Type"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Defines the category into which this invoice falls for mod 347 report."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Defines the category into which this invoice falls for mod 349 report"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Description"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid "Direct Debit Account"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_general_ledger_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_generic_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_report__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return_type__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_libros_registro_export_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod111_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod115_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod130_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod303_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod347_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod349_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod390_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_tax_report_handler__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Document"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "E - Supply"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__b
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_key__b
msgid "Entregas de bienes y prestaciones de servicios"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Entry"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid "Exempted From Mod 390 Available"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390
msgid "Exempted From Modelo 390"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__g
msgid "G - Income to enter on CCT"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__g
msgid "G - Tributaria Current Account - Income"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "General Ledger - %(date_from)s_%(date_to)s"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod130_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Generate BOE"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
#, python-format
msgid "Go to Company"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__group_number
msgid "Group of entities - Group Number"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "H - Supply without taxes delivered by a legal representative"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__i
msgid "I - Income"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "I - Services acquisition"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid "IAE Group or Heading"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_general_ledger_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_generic_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_report__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_return_type__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_libros_registro_export_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod111_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod115_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod130_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod303_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod347_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod349_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_mod390_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_tax_report_handler__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__id
msgid "ID"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__monthly_return
msgid "In Monthly Return Register"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__insurance
msgid "Insurance operation"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Judicial person"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_id
msgid "L10N Es Reports Boe Wizard"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_model
msgid "L10N Es Reports Boe Wizard Model"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Libro Diario XLSX"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Line"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "M - Supply without taxes"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid ""
"Manual entries containing the amounts perceived for the partners with cash "
"basis criterion during this year. Leave empty for partners for which this "
"criterion does not apply."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_mod347_manual_partner_data
msgid "Manually Entered Data for Mod 347 Report"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__monthly_return
msgid "Monthly return record in some period of the fiscal year"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__n
msgid "N - No Activity / Zero Result"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__n
msgid "N - To return"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Name of the contact person fot this BOE file's submission"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__physical_person_name
msgid "Natural Person - Name"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "No TIN set for partner %(name)s (id %(id)d). Please define one."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid ""
"Number of the previous report, corrected or replaced by this one, if any"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
msgid "Number of the report previously submitted"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__seguros
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__seguros
msgid "Operaciones de Seguros"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_class
msgid "Operation Class"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_key
msgid "Operation Key"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this BOE wizard, "
"if there is one."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__otras
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_real_estates_vat__operation_class__otras
msgid "Otras operaciones"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__parent_wizard_id
msgid "Parent Wizard"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_in_tax_unit
msgid "Part of a tax unit"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__partner_id
msgid "Partner"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is located in Spain but does not have any "
"province. Please set one."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is not associated to any Spanish province, and "
"should hence have a country code. For this, fill in its 'country' field."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__perceived_amount
msgid "Perceived Amount"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Phone number where to join the contact person"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
#, python-format
msgid "Please configure the \"IAE Group or Heading\" of your company."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first assign a BIC number to the bank related to this account."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first set the TIN of your company."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "Please select a Spanish invoice type for this invoice."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please select an IBAN account."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid "Previous Report Number"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__previous_decl_number
msgid "Previous declaration no."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_activity
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Principal activity"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_code_activity
msgid "Principal activity - Activity Code"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_iae_epigrafe
msgid "Principal activity - Epígrafe"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Print BOE"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "R - Transfers of goods made under consignment sales agreements"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Reading a non-Spanish TIN as a Spanish TIN."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Refund Invoice %s was created without a link to the original invoice that "
"was credited, while we need that information for this report. "
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__regular
msgid "Regular operation"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__report_id
msgid "Report"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_nif
msgid "Representative - NIF"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_name
msgid "Representative - Name and Surname"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_notary
msgid "Representative - Notary"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_procuration_date
msgid "Representative - Power of Attorney Date"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "S - Services sale"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_libros_registro_export_handler
msgid "Spanish Libros Registro de IVA"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_tax_report_handler
msgid "Spanish Tax Report Custom Handler"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod111_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod111)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod115_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod115)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod130_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod130)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod303_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod303)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod347_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod347)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod349_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod349)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod390_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod390)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_regime_applicable_163
msgid "Special Regime Art. 163 is applicable"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Special Regimen"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis
msgid "Special cash basis regime"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Substitute declaration"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Substitutive Declaration"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_decl_by_rectif_of_quotas
msgid "Substitutive declaration for correction of quotas"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_declaration
msgid "Substitutive declaration?"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "T - Triangular Operation"
msgstr ""

#. module: l10n_es_reports
#: model:account.return.type,name:l10n_es_reports.es_mod303_tax_return_type
msgid "Tax Return (Mod 303) (ES)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_id
msgid "Taxpayer ID"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_first_name
msgid "Taxpayer first name"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__taxpayer_last_name
msgid "Taxpayer last name"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid ""
"Technical field used to only make exempted_from_mod_390 avilable in the last"
" period (12 or 4T)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid ""
"The IBAN account number to use for direct debit. Leave blank if you don't "
"use direct debit."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "This report export is only available for ES companies."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid "Trimester monthly report"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid ""
"True if and only if the invoice MIGHT need to be reported on mod 347, i.e. "
"it concerns an operation from a Spanish headquarter."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid ""
"True if and only if the invoice must be reported on mod 349 report, i.e. it "
"concerns an intracommunitary operation."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Type for mod 347"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Type for mod 349"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__u
msgid "U - Direct Debit of the Income in CCC"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod130_export_wizard__declaration_type__u
msgid "U - Direct debit"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "Unable to find matching surcharge tax in %s"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__using_sii
msgid "Using SII Voluntarily"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__v
msgid "V - Tributaria Current Account - Return"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "VAT Record Books (XLSX)"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid ""
"Whether or not this BOE file corresponds to a complementary declaration"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Whether or not this BOE file corresponds to a substitutive declaration"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod130_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a complementary declaration."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a corrective self-assessment."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid ""
"Whether or not this BOE file must be generated with the data of the first "
"two months of the trimester (in case its total amount of operation is above "
"the threshold fixed by the law)"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of a year."
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of one month "
"or a trimester."
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__x
msgid "X - Return by Transfer Abroad (Only for Periods 3T, 4T and 07 to 12)"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "XLSX"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"You cannot generate a BOE file for the first two months of a trimester if "
"only one month is selected!"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Your date range does not cover entire months, please use a start and end "
"date matching respectively the first and last day of a month."
msgstr ""
