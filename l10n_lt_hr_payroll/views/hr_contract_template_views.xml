<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_template_view_form" model="ir.ui.view">
        <field name="name">hr.contract.template.view.form.inherit.l10_lt_hr_payroll</field>
        <field name="model">hr.version</field>
        <field name="inherit_id" ref="hr.hr_contract_template_form_view"/>
        <field name="arch" type="xml">
            <group name="salary_left" position="inside">
                <label for="l10n_lt_benefits_in_kind" invisible="country_code != 'LT'"/>
                <div class="o_row" invisible="country_code != 'LT'">
                    <field name="l10n_lt_benefits_in_kind" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
                <field name="l10n_lt_time_limited" invisible="country_code != 'LT'"/>
                <field name="l10n_lt_pension" invisible="country_code != 'LT'"/>
            </group>
        </field>
    </record>
</odoo>
