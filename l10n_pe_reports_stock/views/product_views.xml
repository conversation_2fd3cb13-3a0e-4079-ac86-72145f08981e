<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.view.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='invoicing']//group[@name='accounting']" position="inside">
                <group name="pe" string="PE" invisible="'PE' not in fiscal_country_codes">
                    <field name="l10n_pe_type_of_existence" />
                </group>
            </xpath>
        </field>
    </record>

    <record id="product_product_stock_tree" model="ir.ui.view">
        <field name="name">product.product.stock.list.inherit.l10n_pe_reports_stock</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="stock.product_product_stock_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button
                    name="action_get_pe_ple_reports"
                    string="PLE Reports"
                    type="object"
                    class="btn-primary ms-1"
                    display="always"
                />
            </xpath>
        </field>
    </record>
</odoo>
