<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.stock.accountant</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@id='default_accounts']" position="after">
                <block title="Stock Valuation" id="stock_valuation">
                    <setting string="Automatic Accounting" help="Enable automatic accounting entries for stock movements" id="default_stock_valuation_accounts">
                        <div name="default_accounts" class="content-group">
                            <div class="row mt8">
                                <label for="valuation_method" class="col-lg-5 o_light_label"/>
                                <field name="valuation_method"/>
                            </div>
                            <div class="row mt8">
                                <label for="inventory_period" string="Periodicity" class="col-lg-5 o_light_label"/>
                                <field name="inventory_period"/>
                            </div>
                            <div class="row mt8">
                                <label for="stock_valuation_account_id" string="Valuation Account" class="col-lg-5 o_light_label"/>
                                <field name="stock_valuation_account_id"/>
                            </div>
                            <div class="row mt8">
                                <label for="stock_journal" string="Journal" class="col-lg-5 o_light_label"/>
                                <field name="stock_journal"/>
                            </div>
                            <div class="row mt8" invisible="not use_anglo_saxon">
                                <label for="account_cogs_id" string="Cogs Account" class="col-lg-5 o_light_label"/>
                                <field name="account_cogs_id"/>
                            </div>
                        </div>
                    </setting>
                </block>
            </xpath>
        </field>
    </record>
</odoo>
