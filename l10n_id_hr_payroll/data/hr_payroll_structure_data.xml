<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_payroll_structure_id_employee_salary" model="hr.payroll.structure">
        <field name="name">Indonesia: Regular Pay</field>
        <field name="code">IDMONTHLY</field>
        <field name="country_id" ref="base.id" />
        <field name="type_id" ref="l10n_id_hr_payroll.structure_type_employee_id" />
        <field name="unpaid_work_entry_type_ids" eval="[
            (4, ref('hr_work_entry.work_entry_type_unpaid_leave')),
        ]"/>
        <field name="rule_ids" eval="[]"/>
    </record>

    <record id="l10n_id_hr_payroll.structure_type_employee_id" model="hr.payroll.structure.type">
        <field name="default_struct_id" ref="hr_payroll_structure_id_employee_salary" />
    </record>

</odoo>
