<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="account_report_gstr3b" model="account.report">
        <field name="name">GSTR-3B</field>
        <field name="country_id" ref="base.in"/>
        <field name="filter_multi_company">tax_units</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="column_ids">
            <record id="account_report_gstr3b_base_column" model="account.report.column">
                <field name="name">Base</field>
                <field name="expression_label">tax_base</field>
            </record>
            <record id="account_report_gstr3b_cgst_column" model="account.report.column">
                <field name="name">CGST</field>
                <field name="expression_label">tax_cgst</field>
            </record>
            <record id="account_report_gstr3b_sgst_column" model="account.report.column">
                <field name="name">SGST/UTGST</field>
                <field name="expression_label">tax_sgst</field>
            </record>
            <record id="account_report_gstr3b_igst_column" model="account.report.column">
                <field name="name">IGST</field>
                <field name="expression_label">tax_igst</field>
            </record>
            <record id="account_report_gstr3b_cess_column" model="account.report.column">
                <field name="name">CESS</field>
                <field name="expression_label">tax_cess</field>
            </record>
        </field>
        <field name="line_ids">
            <!-- 3.1 Details of Outward Supplies and inward supplies liable to reverse charge -->
            <record id="account_report_gstr3b_3_1" model="account.report.line">
                <field name="name">3.1 Details of Outward Supplies and inward supplies liable to reverse charge</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_report_gstr3b_3_1_expression_tax_base" model="account.report.expression">
                        <field name="label">tax_base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_base + 3_1_B.tax_base + 3_1_C.tax_base + 3_1_D.tax_base + 3_1_E.tax_base</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_cgst" model="account.report.expression">
                        <field name="label">tax_cgst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_cgst + 3_1_D.tax_cgst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_sgst" model="account.report.expression">
                        <field name="label">tax_sgst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_sgst + 3_1_D.tax_sgst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_igst" model="account.report.expression">
                        <field name="label">tax_igst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_igst + 3_1_B.tax_igst + 3_1_D.tax_igst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_cess" model="account.report.expression">
                        <field name="label">tax_cess</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_cess + 3_1_B.tax_cess + 3_1_D.tax_cess</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_3_1_a" model="account.report.line">
                        <field name="name">(a) Outward Taxable supplies (other than zero rated, nil rated and exempted)</field>
                        <field name="code">3_1_A</field>
                            <field name="expression_ids">
                                <record id="account_report_gstr3b_3_1_a_tax_base" model="account.report.expression">
                                    <field name="label">tax_base</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">b2b.tax_base + b2cl.tax_base + b2cs.tax_base + de.tax_base + cdnr_b2b_regular.tax_base + cdnr_b2b_reverse_charge.tax_base + cdnr_de.tax_base + cdnur_b2cl.tax_base</field>
                                    <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_cgst" model="account.report.expression">
                                    <field name="label">tax_cgst</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">b2b.tax_cgst + b2cs.tax_cgst + de.tax_cgst + cdnr_b2b_regular.tax_cgst + cdnr_de.tax_cgst</field>
                                    <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_sgst" model="account.report.expression">
                                    <field name="label">tax_sgst</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">b2b.tax_sgst + b2cs.tax_sgst + de.tax_sgst + cdnr_b2b_regular.tax_sgst + cdnr_de.tax_sgst</field>
                                    <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_igst" model="account.report.expression">
                                    <field name="label">tax_igst</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">b2b.tax_igst + b2cl.tax_igst + b2cs.tax_igst + de.tax_igst + cdnr_b2b_regular.tax_igst + cdnr_de.tax_igst + cdnur_b2cl.tax_igst</field>
                                    <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_cess" model="account.report.expression">
                                    <field name="label">tax_cess</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">b2b.tax_cess + b2cl.tax_cess + b2cs.tax_cess + de.tax_cess + cdnr_b2b_regular.tax_cess + cdnr_de.tax_cess + cdnur_b2cl.tax_cess</field>
                                    <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                                </record>
                            </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_b" model="account.report.line">
                        <field name="name">(b) Outward Taxable supplies (zero rated)</field>
                        <field name="code">3_1_B</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_b_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">exp.tax_base + sez.tax_base + cdnr_sezwp.tax_base + cdnr_sezwop.tax_base + cdnur_expwp.tax_base + cdnur_expwop.tax_base</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_b_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">exp.tax_igst + sez.tax_igst + cdnr_sezwp.tax_igst + cdnur_expwp.tax_igst</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_b_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">exp.tax_cess + sez.tax_cess + cdnr_sezwp.tax_cess + cdnur_expwp.tax_cess</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_c" model="account.report.line">
                        <field name="name">(c) Other Outward Taxable supplies (Nil rated, exempted)</field>
                        <field name="code">3_1_C</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_c_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">nil_rated.tax_base + exempted.tax_base</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_d" model="account.report.line">
                        <field name="name">(d) Inward supplies (liable to reverse charge)</field>
                        <field name="code">3_1_D</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_d_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('display_type', '=', 'product')]"/>
                                <field name="subformula">sum</field>
                            </record>
                             <record id="account_report_gstr3b_3_1_d_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cgst'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_sgst'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_e" model="account.report.line">
                        <field name="name">(e) Non-GST Outward supplies</field>
                        <field name="code">3_1_E</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_e_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">non_gst.tax_base</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 3.1.1 Details of Supplies notified under section 9(5) of the CGST Act, 2017 and corresponding provisions in IGST/UTGST/SGST Acts -->
            <record id="account_report_gstr3b_3_1_1" model="account.report.line">
                <field name="name">3.1.1 Details of Supplies notified under section 9(5) of the CGST Act, 2017 and corresponding provisions in IGST/UTGST/SGST Acts</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_3_1_1_a" model="account.report.line">
                        <field name="name">(i) Taxable supplies on which electronic commerce operator pays tax u/s 9(5) [to be furnished by electronic commerce operator] (Manual)</field>
                        <field name="code">3_1_1_A</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_1_a_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_1_a_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_1_a_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_1_a_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_1_a_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_1_b" model="account.report.line">
                        <field name="name">(ii) Taxable supplies made by registered person through electronic commerce operator, on which electronic commerce operator is required to pay tax u/s 9(5) [to be furnished by registered person making supplies through electronic commerce operator]</field>
                        <field name="code">3_1_1_B</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_1_b_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">eco_9_5.tax_base</field>
                                <field name="subformula">cross_report(l10n_in_reports.account_report_gstr1)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 3.2 Out of the supplies shown in 3.1 (a), details of inter-state supplies made to unregistered persons, composition taxable person and UIN holders -->
            <record id="account_report_gstr3b_3_2" model="account.report.line">
                <field name="name">3.2 Out of the supplies shown in 3.1 (a), details of inter-state supplies made to unregistered persons, composition taxable person and UIN holders</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_3_2_ur" model="account.report.line">
                        <field name="name">Supplies made to Unregistered Persons</field>
                        <field name="code">3_2_UR</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_ur_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_UR_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_UR_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_UR_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_UR_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_UR_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_UR_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_UR_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_UR_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_UR_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_UR_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_UR_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_UR_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_UR_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_UR_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_UR_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_UR_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_UR_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_UR_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_UR_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_UR_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_UR_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_UR_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_UR_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_UR_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_UR_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_UR_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_UR_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_UR_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_UR_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_UR_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_UR_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_UR_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_UR_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_UR_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_UR_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_UR_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_UR_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2cl', 'sale_b2cs', 'sale_cdnur_b2cl']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_2_cp" model="account.report.line">
                        <field name="name">Supplies made to Composition Taxable Persons</field>
                        <field name="code">3_2_CP</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_cp_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_CP_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_CP_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_CP_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_CP_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_CP_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_CP_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_CP_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_CP_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_CP_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_CP_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_CP_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_CP_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_CP_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_CP_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_CP_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_CP_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_CP_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_CP_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_CP_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_CP_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_CP_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_CP_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_CP_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_CP_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_CP_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_CP_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_CP_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_CP_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_CP_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_CP_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_CP_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_CP_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_CP_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_CP_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_CP_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_CP_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_CP_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_2_uin" model="account.report.line">
                        <field name="name">Supplies made to UIN holders</field>
                        <field name="code">3_2_UIN</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_uin_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_UIN_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_UIN_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_UIN_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_UIN_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_UIN_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_UIN_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_UIN_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_UIN_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_UIN_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_UIN_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_UIN_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_UIN_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_UIN_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_UIN_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_UIN_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_UIN_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_UIN_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_UIN_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_UIN_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_UIN_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_UIN_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_UIN_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_UIN_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_UIN_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_UIN_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_UIN_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_UIN_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_UIN_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_UIN_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_UIN_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_UIN_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_UIN_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_UIN_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_UIN_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_UIN_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_UIN_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_UIN_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['sale_b2b_rcm', 'sale_b2b_regular', 'sale_cdnr_rcm', 'sale_cdnr_regular']), ('move_id.l10n_in_gst_treatment', '=', 'uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 4 Eligible ITC -->
            <record id="account_report_gstr3b_4" model="account.report.line">
                <field name="name">4. Eligible ITC</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_4_a" model="account.report.line">
                        <field name="name">(A) ITC Available (Whether in full or part)</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_4_a_1" model="account.report.line">
                                <field name="name">(1) Import of goods</field>
                                <field name="code">4_A_1</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_1_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_imp_goods'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_1_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_imp_goods'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_2" model="account.report.line">
                                <field name="name">(2) Import of services</field>
                                <field name="code">4_A_2</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_2_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_imp_services'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_2_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_imp_services'), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_3" model="account.report.line">
                                <field name="name">(3) Inward supplies liable to reverse charge (other than 1 &amp; 2 above)</field>
                                <field name="code">4_A_3</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_3_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_sgst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_rcm', 'purchase_b2c_rcm', 'purchase_cdnr_rcm', 'purchase_cdnur_rcm']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_4" model="account.report.line">
                                <field name="name">(4) Inward supplies from ISD (Manual)</field>
                                <field name="code">4_A_4</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_4_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_5" model="account.report.line">
                                <field name="name">(5) All other ITC</field>
                                <field name="code">4_A_5</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_5_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_regular', 'purchase_b2c_regular', 'purchase_cdnr_regular', 'purchase_cdnur_regular']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_5_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_regular', 'purchase_b2c_regular', 'purchase_cdnr_regular', 'purchase_cdnur_regular']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_5_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_regular', 'purchase_b2c_regular', 'purchase_cdnr_regular', 'purchase_cdnur_regular']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_5_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', 'in', ['purchase_b2b_regular', 'purchase_b2c_regular', 'purchase_cdnr_regular', 'purchase_cdnur_regular']), ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_total" model="account.report.line">
                                <field name="name">TOTAL</field>
                                <field name="code">4_A_T</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_total_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_A_3.tax_cgst + 4_A_4.tax_cgst + 4_A_5.tax_cgst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_total_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_A_3.tax_sgst + 4_A_4.tax_sgst + 4_A_5.tax_sgst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_total_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_A_1.tax_igst + 4_A_2.tax_igst + 4_A_3.tax_igst + 4_A_4.tax_igst + 4_A_5.tax_igst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_total_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_A_1.tax_cess + 4_A_2.tax_cess + 4_A_3.tax_cess + 4_A_4.tax_cess + 4_A_5.tax_cess</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_4_b" model="account.report.line">
                        <field name="name">(B) ITC Reversed</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_4_b_1" model="account.report.line">
                                <field name="name">(1) As per rules 38,42 &amp; 43 of CGST Rules and section 17(5)</field>
                                <field name="code">4_B_1</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_b_1_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_b_2" model="account.report.line">
                                <field name="name">(2) Others</field>
                                <field name="code">4_B_2</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_b_2_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type', 'in', ['entry', 'in_invoice', 'in_receipt', 'in_refund']), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_b_total" model="account.report.line">
                                <field name="name">TOTAL</field>
                                <field name="code">4_B_T</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_b_total_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_B_1.tax_cgst + 4_B_2.tax_cgst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_total_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_B_1.tax_sgst + 4_B_2.tax_sgst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_total_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_B_1.tax_igst + 4_B_2.tax_igst</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_total_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">4_B_1.tax_cess + 4_B_2.tax_cess</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_4_c" model="account.report.line">
                        <field name="name">(C) Net ITC available (A-B)</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_4_c_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">4_A_T.tax_cgst - 4_B_T.tax_cgst</field>
                            </record>
                            <record id="account_report_gstr3b_4_c_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">4_A_T.tax_sgst - 4_B_T.tax_sgst</field>
                            </record>
                            <record id="account_report_gstr3b_4_c_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">4_A_T.tax_igst - 4_B_T.tax_igst</field>
                            </record>
                            <record id="account_report_gstr3b_4_c_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">4_A_T.tax_cess - 4_B_T.tax_cess</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_4_d" model="account.report.line">
                        <field name="name">(D) Other Details</field>
                        <field name="code">4_D_1</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_4_d_1" model="account.report.line">
                                <field name="name">(1) ITC reclaimed which was reversed under Table 4(B)(2) in earlier tax period (Manual)</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_d_1_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_1_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_1_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_1_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_d_2" model="account.report.line">
                                <field name="name">(2) Ineligible ITC under section 16(4) &amp; ITC restricted due to Pos rules (Manual)</field>
                                <field name="code">4_D_2</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_d_2_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_2_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_2_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_d_2_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 5. Values of exempt, Nil-rated and non-GST inward supplies -->
            <record id="account_report_gstr3b_5" model="account.report.line">
                <field name="name">5. Values of exempt, Nil-rated and non-GST inward supplies</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_5_a" model="account.report.line">
                        <field name="name">From a supplier under composition scheme, Exempt and Nil rated supply</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_5_a_1" model="account.report.line">
                                <field name="name">Inter-State supplies</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_5_a_1_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="['|', ('l10n_in_gstr_section', 'in', ['purchase_nil_rated', 'purchase_exempt']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('display_type', '=', 'product')]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_5_a_2" model="account.report.line">
                                <field name="name">Intra-State supplies</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_5_a_2_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="['|', ('l10n_in_gstr_section', 'in', ['purchase_nil_rated', 'purchase_exempt']), ('move_id.l10n_in_gst_treatment', '=', 'composition'), ('move_id.l10n_in_transaction_type', '=', 'intra_state'), ('display_type', '=', 'product')]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_5_b" model="account.report.line">
                        <field name="name">Non GST supply</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_5_b_1" model="account.report.line">
                                <field name="name">Inter-State supplies</field>
                                <field name="code">INTER_NONGST</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_5_b_1_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_non_gst_supplies'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('display_type', '=', 'product')]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_5_b_2" model="account.report.line">
                                <field name="name">Intra-State supplies</field>
                                <field name="code">INTRA_NONGST</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_5_b_2_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('l10n_in_gstr_section', '=', 'purchase_non_gst_supplies'), ('move_id.l10n_in_transaction_type', '=', 'intra_state'), ('display_type', '=', 'product')]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <record id="account_report_gstr3b_5_1" model="account.report.line">
                <field name="name">5.1 Interest and Late fee for previous tax period</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_5_1_a" model="account.report.line">
                        <field name="name">Interest (Manual)</field>
                        <field name="code">5_1_A</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_5_1_a_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_5_1_a_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_5_1_a_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_5_1_a_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_5_1_b" model="account.report.line">
                        <field name="name">Late Fees (Manual)</field>
                        <field name="code">5_1_B</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_5_1_b_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                            <record id="account_report_gstr3b_5_1_b_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>

    <record id="action_l10n_in_gstr3b" model="ir.actions.client">
        <field name="name">GSTR-3B Report</field>
        <field name="tag">account_report</field>
        <field name="context" eval="{'report_id': ref('l10n_in_reports.account_report_gstr3b')}"/>
    </record>

    <menuitem id="menu_account_report_gstr3b" name="GSTR-3B" sequence="6" parent="l10n_in.account_reports_in_statements_menu" action="action_l10n_in_gstr3b" groups="base.group_no_one"/>

</odoo>
