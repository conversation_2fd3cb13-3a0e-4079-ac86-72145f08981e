<odoo>
    <data noupdate="1">
        <record id="ksa_saudi_employee_payroll_structure" model="hr.payroll.structure">
            <field name="name">Saudi Arabia: Monthly Pay</field>
            <field name="country_id" ref="base.sa"/>
            <field name="type_id" ref="l10n_sa_hr_payroll.ksa_employee_payroll_structure_type"/>
            <field name="rule_ids" eval="[]"/>
        </record>
        <record id="l10n_sa_salary_advance_and_loan" model="hr.payroll.structure">
            <field name="name">SA Salary Advance And Loan Structure</field>
            <field name="code">SALARYADVANDLOAN</field>
            <field name="country_id" ref="base.sa"/>
            <field name="report_id" ref="l10n_sa_hr_payroll.action_report_advance_and_loan"/>
            <field name="payslip_name">Advance and Loan</field>
            <field name="type_id" ref="l10n_sa_hr_payroll.ksa_employee_payroll_structure_type"/>
            <field name="use_worked_day_lines" eval="False"/>
            <field name="rule_ids" eval="[]"/>
        </record>
        <record id="l10n_sa_hr_payroll.ksa_employee_payroll_structure_type" model="hr.payroll.structure.type">
            <field name="default_struct_id" ref="ksa_saudi_employee_payroll_structure"/>
        </record>
    </data>
</odoo>
