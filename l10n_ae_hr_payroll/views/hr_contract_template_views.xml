<odoo>
    <record id="hr_contract_template_view_form" model="ir.ui.view">
        <field name="name">hr.contract.template.view.form.inherit.l10_ae_hr_payroll</field>
        <field name="model">hr.version</field>
        <field name="inherit_id" ref="hr.hr_contract_template_form_view"/>
        <field name="arch" type="xml">
            <group name="salary_left" position="inside" >
                <label for="l10n_ae_housing_allowance" invisible="country_code != 'AE'"/>
                <div class="o_row" name="l10n_ae_housing_allowance" invisible="country_code != 'AE'">
                    <field name="l10n_ae_housing_allowance" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
                <label for="l10n_ae_transportation_allowance" invisible="country_code != 'AE'"/>
                <div class="o_row" name="l10n_ae_transportation_allowance" invisible="country_code != 'AE'">
                    <field name="l10n_ae_transportation_allowance" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
                <label for="l10n_ae_other_allowances" invisible="country_code != 'AE'"/>
                <div class="o_row" name="l10n_ae_other_allowances" invisible="country_code != 'AE'">
                    <field name="l10n_ae_other_allowances" class="o_hr_narrow_field"/>
                    <span>/ month</span>
                </div>
            </group>
            <group name="salary_info" position="after">
                <group>
                    <group name="annual_leave_provision" string="Annual Leave Provision" invisible="country_code != 'AE'">
                        <field name="l10n_ae_number_of_leave_days"/>
                    </group>
                    <group name="dews_benefits" string="DEWS Benefits" invisible="country_code != 'AE'">
                        <field name="l10n_ae_is_dews_applied"/>
                    </group>
                </group>
                <group name="ae_eos" string="End Of Service" invisible="country_code != 'AE'">
                    <label for="l10n_ae_is_computed_based_on_daily_salary"/>
                    <div name="o_row">
                        <field name="l10n_ae_is_computed_based_on_daily_salary"/>
                        <field name="l10n_ae_eos_daily_salary" invisible="not l10n_ae_is_computed_based_on_daily_salary" class="o_hr_narrow_field"/>
                    </div>
                </group>
            </group>
        </field>
    </record>
</odoo>
