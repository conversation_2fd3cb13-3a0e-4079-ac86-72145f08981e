<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <t t-name="frontdesk.Many2One">
        <div class="o_input_dropdown">
            <AutoComplete
                value="props.value"
                id="props.id"
                placeholder="props.placeholder || 'Write the name of the person you\'re visiting....'"
                sources="sources"
                onInput="() => props.update(null)"
                dropdown="true"
                autoSelect="true"
                autofocus="true"
            >
                <t t-set-slot="option" t-slot-scope="optionScope">
                    <span class="o_avatar_many2x_autocomplete o_avatar d-flex align-items-center">
                        <img t-if="optionScope.data.id" class="rounded me-1" t-attf-src="/web/image/hr.employee.public/{{optionScope.data.id}}/avatar_128"/>
                        <t t-esc="optionScope.label"/>
                    </span>
                </t>
            </AutoComplete>
        </div>
    </t>
</templates>
