# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_br_avatax
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.5a1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-14 09:44+0000\n"
"PO-Revision-Date: 2025-07-14 09:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"%(transaction)s is a goods transaction but has service products:\n"
"%(products)s."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"%(transaction)s is a service transaction but has non-service products:\n"
"%(products)s"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "%s must have a city selected in the list of Brazil's cities."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Create a new account\" role=\"img\" aria-label=\"Create a new account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Create a new account"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Create account\" role=\"img\" aria-label=\"Create account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Create account"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Show logs"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                    Start logging for 30 minutes"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Test connection"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Transfer Avalara API credentials</span>"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "A product is required on each line when using Avatax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_cest_code
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_cest_code
msgid ""
"A tax classification code used to identify goods and products subject to tax"
" substitution under ICMS regulations.It helps determine the applicable tax "
"treatment and procedures for specific items.Check if your product is subject"
" or not to this in https://www.codigocest.com.br/."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "API ID"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Accounts in sandbox and production are independent. Create a new account in "
"each environment when needed. It is not recommended to use sandbox "
"credentials on a production database."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__active
msgid "Active"
msgstr ""

#. module: l10n_br_avatax
#: model:product.template,name:l10n_br_avatax.added_taxes_consumable_product_product_template
msgid "Added Taxes Consumable Product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__agricultural_production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__agricultural_production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__agricultural_production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__agricultural_production
msgid "Agricultural production"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Brazil"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Company Configuration"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Configuration Steps"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Credentials"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_api_identifier
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_api_identifier
msgid "Avalara Brazil API ID"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_api_key
msgid "Avalara Brazil API KEY"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_api_key
msgid "Avalara Brazil API Key"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avalara_environment
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avalara_environment
msgid "Avalara Brazil Environment"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.actions.act_window,name:l10n_br_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Avalara portal"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Avatax Brazil doesn't support negative lines."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__l10n_br_avatax_code
msgid "Avatax Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_portal_email
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_portal_email
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Avatax Portal Email"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Avatax requires at least one non-transport line."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.view_product_template_form
msgid "Brazil Accounting"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_property_service_code_origin_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_property_service_code_origin_id
msgid "Brazil: City service code where the provider is registered."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_tax_regime
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_tax_regime
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_tax_regime
msgid "Brazil: Contact FederalTax Regime"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_sped_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_sped_type
msgid "Brazil: Fiscal product type according to SPED list table"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move_line__l10n_br_goods_operation_type_id
msgid ""
"Brazil: If an Operation Type is selected, it will be applied to the product "
"in the line, determining the CFOP for that line. If no selection is made, "
"the operation type will be inherited from the header."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_is_subject_csll
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_is_subject_csll
msgid ""
"Brazil: If not checked, then it will be treated as Exempt. There are cases "
"where both seller, buyer, and items are taxable but a special situation "
"forces the transaction to be CSLL exempt. This attribute allows users to "
"identify such scenarios and trigger the exemption despite all other "
"settings."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_labor
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_labor
msgid "Brazil: If your service involves labor, select this checkbox."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_iss_simples_rate
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_iss_simples_rate
msgid ""
"Brazil: In case the customer or the seller - company - is in the Simplified "
"Regime, the seller - company - needs to inform the ISS rate."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_activity_sector
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_activity_sector
msgid "Brazil: List of main Activity Sectors of the contact"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_company__l10n_br_cnae_code_id
msgid "Brazil: Main CNAE code registered with the government."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_ncm_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_ncm_code_id
msgid ""
"Brazil: NCM (Nomenclatura Comun do Mercosul) Code from the Mercosur List"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_source_origin
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_source_origin
msgid ""
"Brazil: Product Source of Origin indicates if the product has a foreing or "
"national origin with different variations and characteristics dependin on "
"the product use case"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_taxpayer
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_taxpayer
msgid ""
"Brazil: Taxpayer Type informs whether the contact is within the ICMS regime,"
" if it is Exempt, or if it is a Non-Taxpayer"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_service_code_ids
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_service_code_ids
msgid ""
"Brazil: The service codes for this product, as defined by the cities in "
"which you wish to sell it. If no city-specific code is provided, the Service"
" Code Origin will be used instead."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_subject_cofins
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_subject_pis
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_subject_cofins
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_subject_pis
msgid ""
"Brazil: There are cases where both seller, buyer, and items are taxable but "
"a special situation forces the transaction to be exempt especially for PIS "
"and COFINS. This attribute allows users to identify such scenarios and "
"trigger the exemption despite all other settings."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_ncm_code__ex
msgid ""
"Brazil: Use this field to indicate an 'EX Citation' which identifies exceptions to Avalara’s standard fiscal rules.\n"
"EX Citations help define specific tax treatments (e.g., CST, ST, rate reductions, special benefits) for products with tax behavior different from Avalara’s default settings."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_use_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_use_type
msgid "Brazil: indicate what is the usage purpose for this product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_transport_cost_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_transport_cost_type
msgid ""
"Brazil: select whether this product will be use to register Freight, "
"Insurance or Other Costs amounts related to the transaction."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_cnae_code_id
msgid "Brazil: the company's CNAE code for tax calculation and EDI."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_goods_operation_type_id
msgid ""
"Brazil: this is the operation type related to the goods transaction. This "
"will be used as a default on transaction lines."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_use_type
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_use_type
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_use_type
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_use_type
msgid ""
"Brazil: this will override the purpose of use for all products sold here."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Brazilian Real is required to calculate taxes with Avatax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_cest_code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_cest_code
msgid "CEST Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_cnae_code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_cnae_code_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "CNAE Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_subject_cofins
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_subject_cofins
msgid "COFINS Details"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_is_subject_csll
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_is_subject_csll
msgid "CSLL Taxable"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/product_template.py:0
msgid "Can't have more than one service code for %s."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__city_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "City"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Click <i>Meu primeiro acesso</i>, enter the email used here, and request a "
"password."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__code
msgid "Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__company_id
msgid "Company"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Configure your company"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Configure your company to work with AvaTax"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_partner.py:0
msgid "Contacts"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Continue Configurations"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Creating a new account will permanently remove your current Avatax account "
"from Odoo, but it won't delete the account on the Avatax side. Do you wish "
"to proceed?"
msgstr ""

#. module: l10n_br_avatax
#: model:iap.service,unit_name:l10n_br_avatax.iap_service_br_avatax
msgid "Credits"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__ex
msgid "EX"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Environment"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__e
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__e
msgid "Exempt"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__feedstock
msgid "Feedstock"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Finish the Avalara setup before using Odoo."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__fixed_assets
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__fixed_assets
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__fixed_assets
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__fixed_assets
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__fixed_assets
msgid "Fixed assets"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"For Brazilian tax calculation you must set a Mercosul NCM Code on the following:\n"
"%(products)s"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__for_merchandise
msgid "For merchandise"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__for_product
msgid "For product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__2
msgid ""
"Foreign goods - Acquired in the internal market (inside Brazil), except "
"those in code 7"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__7
msgid ""
"Foreign goods - Acquired inside Brazil, without a National Equivalent as "
"listed by Comex and natural gas"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__6
msgid ""
"Foreign goods - Directly imported by Seller, without a National Equivalent "
"as listed by Comex and natural gas"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__1
msgid "Foreign goods - Imported directly by seller, except those in code 6"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__freight
#: model:product.template,name:l10n_br_avatax.freight_transport_product_product_template
msgid "Freight"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Go to company configuration"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Go to the configuration panel"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_goods_operation_type_id
msgid "Goods Operation Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__icms
msgid "ICMS Taxpayer"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_taxpayer
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_taxpayer
msgid "ICMS Taxpayer Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move_line__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__id
msgid "ID"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_iss_simples_rate
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_iss_simples_rate
msgid "ISS Simplified Rate"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__insurance
#: model:product.template,name:l10n_br_avatax.insurance_transport_product_product_template
msgid "Insurance"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__intermediate_product
msgid "Intermediate product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_is_avatax
msgid "Is Brazilian Avatax"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_is_service_transaction
msgid "Is Service Transaction"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_show_overwrite_warning
msgid "L10N Br Avatax Show Overwrite Warning"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_avatax_warnings
msgid "L10N Br Avatax Warnings"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_company_city_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_company_city_id
msgid "L10N Br Company City"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_labor
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_labor
msgid "Labor Assignment"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_activity_sector
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_activity_sector
msgid "Main Activity Sector"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_cnae_code_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Main CNAE code registered with the government."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__material_for_usage_and_consumption
msgid "Material for usage and consumption"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_ncm_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_ncm_code_id
msgid "Mercosul NCM Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_external_tax_mixin
msgid "Mixin to manage common parts of external tax calculation"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_ncm_code
msgid "NCM Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.actions.act_window,name:l10n_br_avatax.l10n_br_ncm_code_action
#: model:ir.ui.menu,name:l10n_br_avatax.ncm_codes_menu
msgid "NCM Codes"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__name
msgid "Name"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__3
msgid ""
"National goods - Merchandise or goods with imported content above 40% and "
"with less than or equal to 70%"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__8
msgid ""
"National goods - Merchandise or goods with imported content above 70% (pt)"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__5
msgid ""
"National goods - Merchandise or goods with imported content equal or below "
"40%"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__0
msgid "National goods - except those treated in codes 3,4, 5 and 8"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__4
msgid ""
"National goods from production following 'standard basic processes' as "
"stablished by legislation (standard basic processes are devised to separate "
"simple assembly from manufaturing processes)"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "Neighborhood"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__no_restriction
msgid "No restriction"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__non
msgid "Non-Taxpayer"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__n
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__n
msgid "Not Taxable"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__notapplicable
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__notapplicable
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__notapplicable
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__notapplicable
msgid "Not applicable"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "Number"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Odoo could not fetch the taxes related to %(document)s."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Only administrators can ping Avatax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_operation_type
msgid "Operation Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.actions.act_window,name:l10n_br_avatax.l10n_br_operation_type_action
#: model:ir.ui.menu,name:l10n_br_avatax.l10n_br_operation_type_menu
msgid "Operation Types"
msgstr ""

#. module: l10n_br_avatax
#: model:product.template,name:l10n_br_avatax.other_cost_transport_product_product_template
msgid "Other Costs"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__other
msgid "Other costs"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__other_inputs
msgid "Other inputs"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move_line__l10n_br_goods_operation_type_id
msgid "Override Operation Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_subject_pis
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_subject_pis
msgid "PIS Details"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__packaging
msgid "Packaging"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Please create an Avatax account"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a complete address on your company."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a valid Avatax portal email."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a valid Tax ID on your company."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_product_product
msgid "Product Variant"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__product_in_process
msgid "Product in process"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_service_code
msgid "Product service codes defined by the city"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_company__l10n_br_avalara_environment__production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__production
msgid "Production"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/product_product.py:0
msgid "Products"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_use_type
msgid "Purpose of Use"
msgstr ""

#. module: l10n_br_avatax
#: model:product.template,name:l10n_br_avatax.regular_consumable_product_product_template
msgid "Regular Consumable Product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__resale
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__resale
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__resale
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__resale
msgid "Resale"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_sped_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_sped_type
msgid "SPED Fiscal Product Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_company__l10n_br_avalara_environment__sandbox
msgid "Sandbox"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__sanitized_code
msgid "Sanitized Code"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "See"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid ""
"Server Response:\n"
"%s"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__service
msgid "Service"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__code
msgid "Service Code"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_property_service_code_origin_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_property_service_code_origin_id
msgid "Service Code Origin"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_service_code_ids
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_service_code_ids
msgid "Service Codes"
msgstr ""

#. module: l10n_br_avatax
#: model:iap.service,description:l10n_br_avatax.iap_service_br_avatax
msgid ""
"Service that allows computing taxes in the Avalara Tax Engine for Brazil. "
"The credits acquired through these packages will be consumed in your "
"production database each time a tax is computed. For more details on the "
"credits consumption, you can consult the Brazilian Functional User "
"Documentation."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Set up to calculate taxes and issue electronic invoices with Avalara AvaTax "
"Brazil."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Set up your AvaTax account"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_icms_rate
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_icms_rate
msgid "Simplified Regime ICMS Rate"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_source_origin
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_source_origin
msgid "Source of Origin"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Steps to configure tax calculation & electronic invoicing"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "Street Name"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__subproduct
msgid "Subproduct"
msgstr ""

#. module: l10n_br_avatax
#: model:product.template,name:l10n_br_avatax.subtracted_consumable_product_product_template
msgid "Subtracted Consumable Product"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__h
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__h
msgid "Suspended"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_tax_regime
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_tax_regime
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_tax_regime
msgid "Tax Regime"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__t
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__t
msgid "Taxable"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__z
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__z
msgid "Taxable With Rate=0.00"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Taxes cannot be calculated for %(record)s:\n"
"%(errors)s"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__exempt
msgid "Taxpayer Exempt"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__technical_name
msgid "Technical Name"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_tax__l10n_br_avatax_code
msgid "Technical field containing the Avatax identifier for this tax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_cnae_code__sanitized_code
msgid ""
"Technical field that contains the code without special characters, as "
"expected by the Avalara API."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_is_avatax
msgid ""
"Technical field used to check if this record requires tax calculation or EDI"
" via Avatax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_is_service_transaction
msgid ""
"Technical field used to determine if this transaction should be sent to the "
"service or goods API."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_show_overwrite_warning
msgid ""
"Technical field used to determine whether or not the user is about to "
"overwrite his current API credentialswith new ones, since the old "
"credentials won't be recoverable we warn."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_company_city_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_company_city_id
msgid ""
"Technical field used to determined the default of a service code when "
"configured as a service code origin."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid ""
"The Avatax platform failed to create your account. Please ensure the address"
" on your company is correct. If it is please contact support at "
"odoo.com/help."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"The Avatax portal email is used to create an Avalara account from Odoo. "
"Complete the account setup in the"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The barcode of %s must have either 8, or 12 to 14 digits when using Avatax."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__city_id
msgid "The city this service code relates to."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__company_id
msgid "The company for which this code applies."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_operation_type__technical_name
msgid ""
"The name that will be sent as operationType to the Brazilian Avatax API."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__code
msgid "The service code for this product as defined by the city."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_icms_rate
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "This only applies if you are a simplified tax regime company."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid ""
"To avoid tax miscalculations make sure to set up %(fields)s on "
"%(partner_name)s."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid ""
"To avoid tax miscalculations make sure to set up %(fields)s on the following:\n"
"%(products)s"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_transport_cost_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_transport_cost_type
msgid "Transport Cost Type"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__l10n_br_is_avatax
msgid "Use Avatax Brazil API"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_external_tax_mixin__l10n_br_use_type__use_or_consumption
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__account_move__l10n_br_use_type__use_or_consumption
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__use_or_consumption
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__sale_order__l10n_br_use_type__use_or_consumption
msgid "Use or consumption"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Use this only when you already created an Avalara account in another Odoo "
"database or company and wish to reuse it."
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.product_product_view_goods_list
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.product_product_view_services_list
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "View"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "View Product(s)"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid "View customer"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid "View products"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Warning: An Avalara account already exists in this database. Creating a new "
"one may cause issues when issuing electronic invoices if credentials or "
"certificates are linked incorrectly. Only continue if you intend to manage "
"separate Avalara accounts per company (e.g., with different IT teams). "
"Otherwise, it is recommended to add your company to the existing Avalara "
"account."
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "What:"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Where:"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__armedforces
msgid "armedForces"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__auctioneer
msgid "auctioneer"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__audiovisualindustry
msgid "audiovisualIndustry"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__bondedwarehouse
msgid "bondedWarehouse"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__broadcastingindustry
msgid "broadcastingIndustry"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__commerce
msgid "commerce"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__construction
msgid "construction"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__coops
msgid "coops"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__distributioncenter
msgid "distributionCenter"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__distributor
msgid "distributor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__electricitydistributor
msgid "electricityDistributor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__energygeneration
msgid "energyGeneration"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__estimatedprofit
msgid "estimatedProfit"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__extractor
msgid "extractor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__farmcoop
msgid "farmCoop"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__filmindustry
msgid "filmIndustry"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__finalconsumer
msgid "finalConsumer"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "for detailed setup instructions and the onboarding process."
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__fueldistributor
msgid "fuelDistributor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__generalwarehouse
msgid "generalWarehouse"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/product_product.py:0
#: code:addons/l10n_br_avatax/models/res_partner.py:0
msgid "id"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__importer
msgid "importer"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__individual
msgid "individual"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__industry
msgid "industry"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__itaipubinacional
msgid "itaipubiNacional"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__maritimeservice
msgid "maritimeService"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__mealsupplier
msgid "mealSupplier"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__nonprofitentity
msgid "nonProfitEntity"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__notapplicable
msgid "notApplicable"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__pharmadistributor
msgid "pharmaDistributor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__publicagency
msgid "publicAgency"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__realprofit
msgid "realProfit"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__religiousestablishment
msgid "religiousEstablishment"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__retail
msgid "retail"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__ruralproducer
msgid "ruralProducer"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "sandbox Avalara portal"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__securitypublicagency
msgid "securityPublicAgency"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__service
msgid "service"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplified
msgid "simplified"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplifiedentrepreneur
msgid "simplifiedEntrepreneur"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplifiedovergrossthreshold
msgid "simplifiedOverGrossthreshold"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__stockwarehouse
msgid "stockWarehouse"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__telco
msgid "telco"
msgstr ""

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "the Odoo documentation"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__transporter
msgid "transporter"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__variable
msgid "variable"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__waterdistributor
msgid "waterDistributor"
msgstr ""

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__wholesale
msgid "wholesale"
msgstr ""
