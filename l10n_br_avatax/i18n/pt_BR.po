# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_br_avatax
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-23 17:02+0000\n"
"PO-Revision-Date: 2024-10-23 16:26-0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.0.1\n"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"%(transaction)s is a goods transaction but has service products:\n"
"%(products)s."
msgstr ""
"%(transaction)s é uma transação de mercadorias mas contém produtos do tipo serviço:\n"
"%(products)s."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"%(transaction)s is a service transaction but has non-service products:\n"
"%(products)s"
msgstr ""
"%(transaction)s é uma transação de serviços mas contém produtos de tipo não-serviço:\n"
"%(products)s"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "%s must have a city selected in the list of Brazil's cities."
msgstr "%s deve selecionar uma cidade brasileira da lista."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"- The following label exceeds the %(max_characters)s character limit for %(doc_type)s: %(line)s"
msgstr ""
"- O seguinte rótulo excede o limite de caracteres de %(max_characters)s em "
"%(doc_type)s: %(line)s"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The barcode of %s must have either 8, or 12 to 14 digits when using Avatax."
msgstr ""
"Ao utilizar a Avalara, o código de barras para %s pode ter 8 dígitos, ou de "
"12 a 14 dígitos."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Create a new account\" role=\"img\" aria-label=\"Create a new account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Create a new account"
msgstr ""
"<i title=\"Create a new account\" role=\"img\" aria-label=\"Create a new account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Criar nova conta"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Create account\" role=\"img\" aria-label=\"Create account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Create account"
msgstr ""
"<i title=\"Create account\" role=\"img\" aria-label=\"Create account\" class=\"fa fa-plug fa-fw\"/>\n"
"                                        Criar conta"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Show logs"
msgstr ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Exibir registros"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                    Start logging for 30 minutes"
msgstr ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                    Iniciar criação de registros por 30 minutos"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Test connection"
msgstr ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Testar conexão"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Transfer Avalara API credentials</span>"
msgstr "<span class=\"o_form_label\">Transferir credenciais da API Avalara</span>"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "A product is required on each line when using Avatax."
msgstr "É necessário haver um produto em cada linha ao usar o Avatax."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_cest_code
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_cest_code
msgid ""
"A tax classification code used to identify goods and products subject to tax"
" substitution under ICMS regulations.It helps determine the applicable tax "
"treatment and procedures for specific items.Check if your product is subject"
" or not to this in https://www.codigocest.com.br/."
msgstr ""
"Um código de classificação fiscal usado para identificar bens e produtos "
"sujeitos à substituição tributária sob as regulamentações do ICMS. Ele ajuda"
" a determinar o tratamento e procedimentos fiscais aplicáveis para itens "
"específicos. Verifique se o seu produto está sujeito ou não a isso em "
"https://www.codigocest.com.br/."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "API ID"
msgstr "ID da API"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "API Key"
msgstr "Chave da API"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de plano da contas"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Accounts in sandbox and production are independent. Create a new account in "
"each environment when needed. It is not recommended to use sandbox "
"credentials on a production database."
msgstr ""
"Contas no sandbox e na produção são independentes. Crie uma nova conta em "
"cada ambiente quando necessário. Não é recomendado usar credenciais de "
"sandbox em um banco de dados de produção."

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__active
msgid "Active"
msgstr "Ativo"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__agricultural_production
msgid "Agricultural production"
msgstr "Produção agrícola"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Brazil"
msgstr "AvaTax Brasil"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Company Configuration"
msgstr "Configuração da empresa AvaTax"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Configuration Steps"
msgstr "Etapas de configuração do AvaTax"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "AvaTax Credentials"
msgstr "Credenciais AvaTax"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_api_identifier
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_api_identifier
msgid "Avalara Brazil API ID"
msgstr "ID de API da Avalara Brasil"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_api_key
msgid "Avalara Brazil API KEY"
msgstr "Chave de API da Avalara Brasil"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_api_key
msgid "Avalara Brazil API Key"
msgstr "Chave de API da Avalara Brasil"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avalara_environment
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avalara_environment
msgid "Avalara Brazil Environment"
msgstr "Ambiente Avalara Brasil"

#. module: l10n_br_avatax
#: model:ir.actions.act_window,name:l10n_br_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr "Registros da Avalara"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Avalara portal"
msgstr ""

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Avatax Brazil doesn't support negative lines."
msgstr "O Avatax Brasil não é compatível com linhas negativas."

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__l10n_br_avatax_code
msgid "Avatax Code"
msgstr "Código Avatax"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_avatax_portal_email
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_portal_email
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Avatax Portal Email"
msgstr "E-mail do portal Avatax"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Avatax requires at least one non-transport line."
msgstr "O Avatax requer pelo menos uma linha de não transporte."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_property_service_code_origin_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_property_service_code_origin_id
msgid "Brazil: City service code where the provider is registered."
msgstr ""
"Brasil: Código de serviço do município em que o fornecedor está registrado."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_tax_regime
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_tax_regime
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_tax_regime
msgid "Brazil: Contact FederalTax Regime"
msgstr "Brasil: Regime de tributação federal do contato"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_sped_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_sped_type
msgid "Brazil: Fiscal product type according to SPED list table"
msgstr "Brasil: Tipo de produto fiscal de acordo com a tabela da lista SPED"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_is_subject_csll
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_is_subject_csll
msgid ""
"Brazil: If not checked, then it will be treated as Exempt. There are cases "
"where both seller, buyer, and items are taxable but a special situation "
"forces the transaction to be CSLL exempt. This attribute allows users to "
"identify such scenarios and trigger the exemption despite all other "
"settings."
msgstr ""
"Brasil: Se desmarcado, será tratado como Isento. Há casos em que o vendedor,"
" o comprador e os itens são tributáveis, mas uma situação particular força a"
" transação como isenta de CSLL. Este atributo permite que usuários "
"identifiquem tais cenários e acionem a isenção a despeito das demais "
"configurações."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_labor
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_labor
msgid "Brazil: If your service involves labor, select this checkbox."
msgstr ""
"Brasil: Se seu serviço envolve mão-de-obra, marque esta caixa de seleção."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_iss_simples_rate
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_iss_simples_rate
msgid ""
"Brazil: In case the customer or the seller - company - is in the Simplified "
"Regime, the seller - company - needs to inform the ISS rate."
msgstr ""
"Brasil: Caso o cliente ou a empresa vendedora esteja no regime Simples "
"Nacional, a empresa vendedora deve informar a taxa de ISS."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_activity_sector
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_activity_sector
msgid "Brazil: List of main Activity Sectors of the contact"
msgstr "Brasil: Lista dos principais setores de atividade do contato"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_company__l10n_br_cnae_code_id
msgid "Brazil: Main CNAE code registered with the government."
msgstr "Brasil: Principal código CNAE registrado no governo."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_ncm_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_ncm_code_id
msgid ""
"Brazil: NCM (Nomenclatura Comun do Mercosul) Code from the Mercosur List"
msgstr ""
"Brasil: Código NCM (Nomenclatura Comum do Mercosul) da Lista do Mercosul"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_source_origin
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_source_origin
msgid ""
"Brazil: Product Source of Origin indicates if the product has a foreing or "
"national origin with different variations and characteristics dependin on "
"the product use case"
msgstr ""
"Brasil: 'Origem do produto' indica se o produto tem origem estrangeira ou "
"nacional com diferentes variações e características, dependendo do caso de "
"uso do produto"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_taxpayer
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_taxpayer
msgid ""
"Brazil: Taxpayer Type informs whether the contact is within the ICMS regime,"
" if it is Exempt, or if it is a Non-Taxpayer"
msgstr ""
"Brasil: 'Tipo de contribuinte' informa se o contato está dentro do regime do"
" ICMS, se é isento ou se é não contribuinte"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_service_code_ids
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_service_code_ids
msgid ""
"Brazil: The service codes for this product, as defined by the cities in "
"which you wish to sell it. If no city-specific code is provided, the Service"
" Code Origin will be used instead."
msgstr ""
"Brasil: Os códigos de serviço deste produto, conforme definidos pelos "
"municípios onde você pretende vendê-los. Se não houver código municipal "
"específico, será utilizada a origem de código de serviço."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_subject_cofins
#: model:ir.model.fields,help:l10n_br_avatax.field_res_partner__l10n_br_subject_pis
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_subject_cofins
#: model:ir.model.fields,help:l10n_br_avatax.field_res_users__l10n_br_subject_pis
msgid ""
"Brazil: There are cases where both seller, buyer, and items are taxable but "
"a special situation forces the transaction to be exempt especially for PIS "
"and COFINS. This attribute allows users to identify such scenarios and "
"trigger the exemption despite all other settings."
msgstr ""
"Brasil: Há casos em que o vendedor, o comprador e os itens são tributáveis, "
"mas uma situação particular força a transação a ser isenta, especialmente "
"para PIS e COFINS. Este atributo permite que os usuários identifiquem tais "
"cenários e acionem a isenção a despeito das demais configurações."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_ncm_code__ex
msgid ""
"Brazil: Use this field to indicate an 'EX Citation' which identifies "
"exceptions to Avalara’s standard fiscal rules.\n"
"EX Citations help define specific tax treatments (e.g., CST, ST, rate "
"reductions, special benefits) for products with tax behavior different from "
"Avalara’s default settings."
msgstr "Brasil: Utilize este campo para indicar uma “EX Citation”, "
"que identifica exceções às regras fiscais padrão da Avalara. "
"As EX Citations ajudam a definir tratamentos fiscais específicos "
"(como CST, ST, reduções de alíquota, benefícios especiais) para "
"produtos com comportamento tributário diferente do padrão da Avalara."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_use_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_use_type
msgid "Brazil: indicate what is the usage purpose for this product"
msgstr "Brasil: indique qual é o propósito de uso deste produto"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_transport_cost_type
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_transport_cost_type
msgid ""
"Brazil: select whether this product will be use to register Freight, "
"Insurance or Other Costs amounts related to the transaction."
msgstr ""
"Brasil: selecione se este produto será usado para registrar valores de "
"frete, seguro ou outros custos relacionados à transação."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_cnae_code_id
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_cnae_code_id
msgid "Brazil: the company's CNAE code for tax calculation and EDI."
msgstr "Brasil: código CNAE da empresa para cálculo de impostos e NFe/NFSe."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_goods_operation_type_id
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_goods_operation_type_id
msgid ""
"Brazil: this is the operation type related to the goods transaction. This "
"will be used as a default on transaction lines."
msgstr ""
"Brasil: este é o tipo de operação é relacionado a transações de mercadorias. Ele"
" será usado como padrão nas linhas de transação."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move_line__l10n_br_goods_operation_type_id
msgid ""
"Brazil: If an Operation Type is selected, it will be applied to the product in the line, "
"determining the CFOP for that line. If no selection is made, the operation type will be "
"inherited from the header."
msgstr ""
"Brasil: Se houver um tipo de operação selecionado, ele será aplicado ao produto na linha, "
"determinando assim o CFOP daquela linha. Se não houver seleção, o tipo de operação "
"será herdado do cabeçalho."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Brazilian Real is required to calculate taxes with Avatax."
msgstr ""
"É necessário usar o Real Brasileiro para calcular impostos com o Avatax."

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_cest_code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_cest_code
msgid "CEST Code"
msgstr "Código CEST"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_cnae_code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_cnae_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_cnae_code_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "CNAE Code"
msgstr "Código CNAE"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_subject_cofins
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_subject_cofins
msgid "COFINS Details"
msgstr "Detalhes de COFINS"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_is_subject_csll
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_is_subject_csll
msgid "CSLL Taxable"
msgstr "Tributável (CSLL)"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/product_template.py:0
msgid "Can't have more than one service code for %s."
msgstr "Não pode haver mais de um código de serviço para %s."

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__city_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "City"
msgstr "Cidade"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Click <i>Meu primeiro acesso</i>, enter the email used here, and request a "
"password."
msgstr ""
"Clique em <i>Meu primeiro acesso</i>, insira o e-mail usado aqui e solicite "
"uma senha."

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__code
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__code
msgid "Code"
msgstr "Código"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__company_id
msgid "Company"
msgstr "Empresa"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Configure your company"
msgstr "Configure sua empresa"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Configure your company to work with AvaTax"
msgstr "Configure sua empresa para trabalhar com o AvaTax"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_partner.py:0
msgid "Contacts"
msgstr "Contatos"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__create_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__create_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__create_date
msgid "Created on"
msgstr "Criado em"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Creating a new account will permanently remove your current Avatax account "
"from Odoo, but it won't delete the account on the Avatax side. Do you wish "
"to proceed?"
msgstr ""
"Criar uma nova conta removerá permanentemente sua conta Avatax atual do "
"Odoo, mas não excluirá a conta do lado do Avatax. Tem certeza de que deseja "
"continuar?"

#. module: l10n_br_avatax
#: model:iap.service,unit_name:l10n_br_avatax.iap_service_br_avatax
msgid "Credits"
msgstr "Créditos"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Environment"
msgstr "Ambiente"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__e
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__e
msgid "Exempt"
msgstr "Isento"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__feedstock
msgid "Feedstock"
msgstr "Matéria-prima"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Finish the Avalara setup before using Odoo."
msgstr "Conclua a configuração do Avalara antes de usar o Odoo."

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posição fiscal"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__fixed_assets
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__fixed_assets
msgid "Fixed assets"
msgstr "Ativos fixos"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"For Brazilian tax calculation you must set a Mercosul NCM Code on the following:\n"
"%(products)s"
msgstr ""
"Para o cálculo de impostos no Brasil, você deve definir um Código NCM Mercosul nos seguintes:\n"
"%(products)s"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__for_merchandise
msgid "For merchandise"
msgstr "Para mercadorias"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__for_product
msgid "For product"
msgstr "Para produto"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__2
msgid ""
"Foreign goods - Acquired in the internal market (inside Brazil), except "
"those in code 7"
msgstr ""
"Bens estrangeiros - Adquiridos no mercado interno (dentro do Brasil), exceto"
" aqueles no código 7"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__7
msgid ""
"Foreign goods - Acquired inside Brazil, without a National Equivalent as "
"listed by Comex and natural gas"
msgstr ""
"Bens estrangeiros - Adquiridos dentro do Brasil, sem um equivalente nacional"
" listado pelo Comex e gás natural"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__6
msgid ""
"Foreign goods - Directly imported by Seller, without a National Equivalent "
"as listed by Comex and natural gas"
msgstr ""
"Bens estrangeiros - Importados diretamente pelo vendedor, sem um equivalente"
" nacional listado pelo Comex e gás natural"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__1
msgid "Foreign goods - Imported directly by seller, except those in code 6"
msgstr ""
"Bens estrangeiros - Importados diretamente pelo vendedor, exceto aqueles no "
"código 6"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__freight
msgid "Freight"
msgstr "Frete"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Go to company configuration"
msgstr "Ir para a configuração da empresa"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Go to the configuration panel"
msgstr "Acessar o painel de configuração"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_goods_operation_type_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_goods_operation_type_id
msgid "Goods Operation Type"
msgstr "Tipo de operação das mercadorias"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__icms
msgid "ICMS Taxpayer"
msgstr "Contribuinte do ICMS"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_taxpayer
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_taxpayer
msgid "ICMS Taxpayer Type"
msgstr "Tipo de contribuinte do ICMS"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_iss_simples_rate
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_iss_simples_rate
msgid "ISS Simplified Rate"
msgstr "Tarifa de ISS simplificada"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__insurance
msgid "Insurance"
msgstr "Seguro"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__intermediate_product
msgid "Intermediate product"
msgstr "Produto intermediário"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_is_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_is_avatax
msgid "Is Brazilian Avatax"
msgstr "É avatax brasileiro"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_is_service_transaction
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_is_service_transaction
msgid "Is Service Transaction"
msgstr "É transação de serviços"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_move
msgid "Journal Entry"
msgstr "Lançamento de diário"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_show_overwrite_warning
msgid "L10N Br Avatax Show Overwrite Warning"
msgstr "L10N Br Avatax - Mostrar aviso de substituição"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_bank_statement_line__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move__l10n_br_avatax_warnings
#: model:ir.model.fields,field_description:l10n_br_avatax.field_sale_order__l10n_br_avatax_warnings
msgid "L10N Br Avatax Warnings"
msgstr "L10N Br Avatax - Avisos"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_company_city_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_company_city_id
msgid "L10N Br Company City"
msgstr "L10N BR - Cidade da empresa"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_labor
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_labor
msgid "Labor Assignment"
msgstr "Atribuição de mão-de-obra"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__write_uid
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__write_date
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_activity_sector
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_activity_sector
msgid "Main Activity Sector"
msgstr "Setor da atividade principal"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_cnae_code_id
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Main CNAE code registered with the government."
msgstr "Principal código CNAE registrado no governo."

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__material_for_usage_and_consumption
msgid "Material for usage and consumption"
msgstr "Material para uso e consumo"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_ncm_code_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_ncm_code_id
msgid "Mercosul NCM Code"
msgstr "Código NCM do Mercosul"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_external_tax_mixin
msgid "Mixin to manage common parts of external tax calculation"
msgstr ""
"Mixin para gerenciamento de partes em comum do cálculo de impostos externo"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_ncm_code
msgid "NCM Code"
msgstr "Código NCM"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_ncm_code__name
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__name
msgid "Name"
msgstr "Nome"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__3
msgid ""
"National goods - Merchandise or goods with imported content above 40% and "
"with less than or equal to 70%"
msgstr ""
"Bens nacionais - Mercadorias ou bens com conteúdo importado acima de 40% e "
"com menos ou igual a 70%"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__8
msgid ""
"National goods - Merchandise or goods with imported content above 70% (pt)"
msgstr ""
"Bens nacionais - Mercadorias ou bens com conteúdo importado acima de 70%"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__5
msgid ""
"National goods - Merchandise or goods with imported content equal or below "
"40%"
msgstr ""
"Bens nacionais - Mercadorias ou bens com conteúdo importado igual ou abaixo "
"de 40%"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__0
msgid "National goods - except those treated in codes 3,4, 5 and 8"
msgstr "Bens nacionais - Exceto aqueles tratados nos códigos 3,4, 5 e 8"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_source_origin__4
msgid ""
"National goods from production following 'standard basic processes' as "
"stablished by legislation (standard basic processes are devised to separate "
"simple assembly from manufaturing processes)"
msgstr ""
"Bens nacionais provenientes de produção seguindo 'processos básicos padrão' "
"conforme estabelecido pela legislação (processos básicos padrão são "
"concebidos para separar a simples montagem de processos de fabricação)"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "Neighborhood"
msgstr "Bairro"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__no_restriction
msgid "No restriction"
msgstr "Sem restrição"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__non
msgid "Non-Taxpayer"
msgstr "Não contribuinte"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__n
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__n
msgid "Not Taxable"
msgstr "Não tributável"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__notapplicable
msgid "Not applicable"
msgstr "Não aplicável"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Odoo could not fetch the taxes related to %(document)s."
msgstr "O Odoo não conseguiu buscar os impostos relacionados a %(document)s."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Only administrators can ping Avatax."
msgstr "Apenas administradores podem fazer ping no Avatax."

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_operation_type
msgid "Operation Type"
msgstr "Tipo de Operação"

#. module: l10n_br_avatax
#: model:ir.actions.act_window,name:l10n_br_avatax.l10n_br_operation_type_action
#: model:ir.ui.menu,name:l10n_br_avatax.l10n_br_operation_type_menu
msgid "Operation Types"
msgstr "Tipos de Operação"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_transport_cost_type__other
msgid "Other costs"
msgstr "Outros custos"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__other_inputs
msgid "Other inputs"
msgstr "Outras entradas"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_move_line__l10n_br_goods_operation_type_id
msgid "Override Operation Type"
msgstr "Sobrescrever tipo de operação"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_subject_pis
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_subject_pis
msgid "PIS Details"
msgstr "Detalhes de PIS"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__packaging
msgid "Packaging"
msgstr "Embalagem"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Please create an Avatax account"
msgstr "Crie uma conta Avatax"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a complete address on your company."
msgstr "Defina um endereço completo para a sua empresa."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a valid Avatax portal email."
msgstr "Defina um e-mail válido do portal Avatax."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Please set a valid Tax ID on your company."
msgstr "Defina um ID fiscal válido em sua empresa."

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_product_template
msgid "Product"
msgstr "Produto"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__product_in_process
msgid "Product in process"
msgstr "Produto em processo"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_l10n_br_service_code
msgid "Product service codes defined by the city"
msgstr "Códigos de serviço definidos pelo município"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__production
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_company__l10n_br_avalara_environment__production
msgid "Production"
msgstr "Produção"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/product_product.py:0
msgid "Products"
msgstr "Produtos"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_use_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_use_type
msgid "Purpose of Use"
msgstr "Propósito de uso"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__resale
msgid "Resale"
msgstr "Revenda"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_sped_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_sped_type
msgid "SPED Fiscal Product Type"
msgstr "Tipo de produto fiscal SPED"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_company__l10n_br_avalara_environment__sandbox
msgid "Sandbox"
msgstr "Sandbox"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_cnae_code__sanitized_code
msgid "Sanitized Code"
msgstr "Código Sanitizado"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "See"
msgstr "Consulte"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid ""
"Server Response:\n"
"%s"
msgstr ""
"Resposta do servidor:\n"
"%s"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid "Continue Configurations"
msgstr "Continuar configurações"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__service
msgid "Service"
msgstr "Serviço"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_service_code__code
msgid "Service Code"
msgstr "Código de serviço"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_property_service_code_origin_id
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_property_service_code_origin_id
msgid "Service Code Origin"
msgstr "Origem do código de serviço"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_service_code_ids
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_service_code_ids
msgid "Service Codes"
msgstr "Códigos de serviço"

#. module: l10n_br_avatax
#: model:iap.service,description:l10n_br_avatax.iap_service_br_avatax
msgid ""
"Service that allows computing taxes in the Avalara Tax Engine for Brazil. "
"The credits acquired through these packages will be consumed in your "
"production database each time a tax is computed. For more details on the "
"credits consumption, you can consult the Brazilian Functional User "
"Documentation."
msgstr ""
"Serviço que permite o cálculo de impostos no Avalara Tax Engine para o "
"Brasil. Os créditos adquiridos por meio desses pacotes serão consumidos em "
"sua base de dados de produção sempre que um imposto for computado. Para "
"obter mais detalhes sobre o consumo de créditos, consulte a Documentação "
"Funcional do Usuário Brasileiro."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Set up to calculate taxes and issue electronic invoices with Avalara AvaTax "
"Brazil."
msgstr ""
"Configure para calcular impostos e emitir notas fiscais eletrônicas com o "
"Avalara AvaTax Brasil."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Set up your AvaTax account"
msgstr "Configure sua conta AvaTax"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_company__l10n_br_icms_rate
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_icms_rate
msgid "Simplified Regime ICMS Rate"
msgstr "Taxa de ICMS do regime simplificado"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_source_origin
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_source_origin
msgid "Source of Origin"
msgstr "Origem"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "Steps to configure tax calculation & electronic invoicing"
msgstr ""
"Passos para configurar o cálculo de impostos e a emissão de notas fiscais "
"eletrônicas"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "Street Name"
msgstr "Rua"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_sped_type__subproduct
msgid "Subproduct"
msgstr "Subproduto"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__h
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__h
msgid "Suspended"
msgstr "Suspenso"

#. module: l10n_br_avatax
#: model:ir.model,name:l10n_br_avatax.model_account_tax
msgid "Tax"
msgstr "Imposto"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_config_settings__l10n_br_tax_regime
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_partner__l10n_br_tax_regime
#: model:ir.model.fields,field_description:l10n_br_avatax.field_res_users__l10n_br_tax_regime
msgid "Tax Regime"
msgstr "Regime tributário"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__t
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__t
msgid "Taxable"
msgstr "Tributável"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_cofins__z
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_subject_pis__z
msgid "Taxable With Rate=0.00"
msgstr "Tributável com a tarifa = 0,00"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Taxes cannot be calculated for %(record)s:\n"
"%(errors)s"
msgstr ""
"Não foi possível calcular os impostos para %(record)s:\n"
"%(errors)s"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_taxpayer__exempt
msgid "Taxpayer Exempt"
msgstr "Contribuinte isento"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_l10n_br_operation_type__technical_name
msgid "Technical Name"
msgstr "Nome técnico"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_tax__l10n_br_avatax_code
msgid "Technical field containing the Avatax identifier for this tax."
msgstr "Campo técnico contendo o identificador Avatax para este imposto."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_cnae_code__sanitized_code
msgid ""
"Technical field that contains the code without special characters, as "
"expected by the Avalara API."
msgstr ""
"Campo técnico que contém o código sem caracteres especiais, conforme "
"esperado pela API Avalara."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_is_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_is_avatax
msgid ""
"Technical field used to check if this record requires tax calculation or EDI"
" via Avatax."
msgstr ""
"Campo técnico utilizado para verificar se este registro necessita de cálculo"
" de imposto ou EDI via Avatax."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_account_bank_statement_line__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_account_external_tax_mixin__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_account_move__l10n_br_is_service_transaction
#: model:ir.model.fields,help:l10n_br_avatax.field_sale_order__l10n_br_is_service_transaction
msgid ""
"Technical field used to determine if this transaction should be sent to the "
"service or goods API."
msgstr ""
"Campo técnico utilizado para determinar se esta transação deve ser enviada "
"para a API de serviços ou de mercadorias."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_avatax_show_overwrite_warning
msgid ""
"Technical field used to determine whether or not the user is about to "
"overwrite his current API credentialswith new ones, since the old "
"credentials won't be recoverable we warn."
msgstr ""
"Campo técnico usado para determinar se o usuário está prestes a substituir "
"suas credenciais atuais de API com credenciais novas. Já que as antigas "
"credenciais não serão recuperáveis, um aviso é exibido."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_product_product__l10n_br_company_city_id
#: model:ir.model.fields,help:l10n_br_avatax.field_product_template__l10n_br_company_city_id
msgid ""
"Technical field used to determined the default of a service code when "
"configured as a service code origin."
msgstr ""
"Campo técnico utilizado para determinar o padrão de um código de serviço "
"configurado como origem de código de serviço."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/res_config_settings.py:0
msgid ""
"The Avatax platform failed to create your account. Please ensure the address"
" on your company is correct. If it is please contact support at "
"odoo.com/help."
msgstr ""
"A plataforma Avatax falhou ao criar sua conta. Certifique-se de que o "
"endereço da sua empresa esteja correto. Se estiver, entre em contato com o "
"suporte em odoo.com/help."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"The Avatax portal email is used to create an Avalara account from Odoo. "
"Complete the account setup in the"
msgstr ""
"O e-mail do portal Avatax é usado para criar uma conta Avalara a partir do "
"Odoo. Complete a configuração da conta no"

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__city_id
msgid "The city this service code relates to."
msgstr "O município ao qual este código de serviço está relacionado."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__company_id
msgid "The company for which this code applies."
msgstr "A empresa a que este código se aplica."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_operation_type__technical_name
msgid ""
"The name that will be sent as operationType to the Brazilian Avatax API."
msgstr ""
"O nome enviado como tipo de operação (operationType) para a API da Avatax do"
" Brasil."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_l10n_br_service_code__code
msgid "The service code for this product as defined by the city."
msgstr "O código de serviço deste produto, conforme definido pelo município."

#. module: l10n_br_avatax
#: model:ir.model.fields,help:l10n_br_avatax.field_res_config_settings__l10n_br_icms_rate
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "This only applies if you are a simplified tax regime company."
msgstr ""
"Isso só se aplica se você for uma empresa do regime tributário simplificado."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid ""
"To avoid tax miscalculations make sure to set up %(fields)s on "
"%(partner_name)s."
msgstr ""
"Para evitar cálculos incorretos, certifique-se de definir %(fields)s em "
"%(partner_name)s."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid ""
"To avoid tax miscalculations make sure to set up %(fields)s on the following:\n"
"%(products)s"
msgstr ""
"Para evitar cálculos incorretos, certifique-se de definir %(fields)s nos seguintes:\n"
"%(products)s"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_product__l10n_br_transport_cost_type
#: model:ir.model.fields,field_description:l10n_br_avatax.field_product_template__l10n_br_transport_cost_type
msgid "Transport Cost Type"
msgstr "Tipo de custo de transporte"

#. module: l10n_br_avatax
#: model:ir.model.fields,field_description:l10n_br_avatax.field_account_fiscal_position__l10n_br_is_avatax
msgid "Use Avatax Brazil API"
msgstr "Use a API do Avatax Brasil"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__product_template__l10n_br_use_type__use_or_consumption
msgid "Use or consumption"
msgstr "Uso ou consumo"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Use this only when you already created an Avalara account in another Odoo "
"database or company and wish to reuse it."
msgstr ""
"Use isso apenas quando você já tiver criado uma conta Avalara em outro banco"
" de dados ou empresa do Odoo e desejar reutilizá-la."

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.product_product_view_goods_list
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.product_product_view_services_list
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_partner_view_list
msgid "View"
msgstr "Visualizar"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "View Product(s)"
msgstr "Visualizar produto(s)"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid "View customer"
msgstr "Visualizar cliente"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
#: code:addons/l10n_br_avatax/models/account_move.py:0
msgid "View products"
msgstr "Visualizar produtos"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid ""
"Warning: An Avalara account already exists in this database. Creating a new "
"one may cause issues when issuing electronic invoices if credentials or "
"certificates are linked incorrectly. Only continue if you intend to manage "
"separate Avalara accounts per company (e.g., with different IT teams). "
"Otherwise, it is recommended to add your company to the existing Avalara "
"account."
msgstr ""
"Aviso: Já existe uma conta Avalara registrada neste banco de dados. "
"Criar uma nova conta pode causar problemas na emissão de notas fiscais eletrônicas "
"se as credenciais ou certificados forem vinculados incorretamente. "
"Prossiga apenas se realmente for necessário gerenciar contas Avalara separadas por empresa "
"(por exemplo, com equipes de IT diferentes). "
"Caso contrário, é recomendável adicionar sua empresa à conta Avalara já existente."

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "What:"
msgstr "O quê:"

#. module: l10n_br_avatax
#. odoo-python
#: code:addons/l10n_br_avatax/models/account_external_tax_mixin.py:0
msgid "Where:"
msgstr "Onde:"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__armedforces
msgid "armedForces"
msgstr "Forças Armadas"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__auctioneer
msgid "auctioneer"
msgstr "Leiloeiro"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__audiovisualindustry
msgid "audiovisualIndustry"
msgstr "Audiovisual"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__bondedwarehouse
msgid "bondedWarehouse"
msgstr "Armazém alfandegado"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__broadcastingindustry
msgid "broadcastingIndustry"
msgstr "Radiodifusão"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__commerce
msgid "commerce"
msgstr "Comércio"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__construction
msgid "construction"
msgstr "Construção civil"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__coops
msgid "coops"
msgstr "Cooperativa"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__distributioncenter
msgid "distributionCenter"
msgstr "Centro de distribuição"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__distributor
msgid "distributor"
msgstr "Distribuidor"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__electricitydistributor
msgid "electricityDistributor"
msgstr "Distribuidor de energia elétrica"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__energygeneration
msgid "energyGeneration"
msgstr "Gerador e produtor de energia elétrica"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__estimatedprofit
msgid "estimatedProfit"
msgstr "Lucro presumido"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__extractor
msgid "extractor"
msgstr "Extrator"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__farmcoop
msgid "farmCoop"
msgstr "Cooperativa de produtores"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__filmindustry
msgid "filmIndustry"
msgstr "Indústria cinematográfica"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__finalconsumer
msgid "finalConsumer"
msgstr "Consumidor final"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "for detailed setup instructions and the onboarding process."
msgstr ""
"para instruções detalhadas de configuração e o processo de integração."

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__fueldistributor
msgid "fuelDistributor"
msgstr "Distribuidor de combustível"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__generalwarehouse
msgid "generalWarehouse"
msgstr "Armazém geral"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__importer
msgid "importer"
msgstr "Importadora"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__individual
msgid "individual"
msgstr "Indivíduo"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__industry
msgid "industry"
msgstr "Indústria"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__itaipubinacional
msgid "itaipubiNacional"
msgstr "Itaipu Binacional"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__maritimeservice
msgid "maritimeService"
msgstr "Serviço marítimo"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__mealsupplier
msgid "mealSupplier"
msgstr "Fornecedor de refeição"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__nonprofitentity
msgid "nonProfitEntity"
msgstr "Entidade sem fins lucrativos"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__notapplicable
msgid "notApplicable"
msgstr "Não aplicável"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__pharmadistributor
msgid "pharmaDistributor"
msgstr "Distribuidor de medicamentos"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__publicagency
msgid "publicAgency"
msgstr "Órgão público"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__realprofit
msgid "realProfit"
msgstr "Lucro real"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__religiousestablishment
msgid "religiousEstablishment"
msgstr "Templo religioso"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__retail
msgid "retail"
msgstr "Varejo"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__ruralproducer
msgid "ruralProducer"
msgstr "Produtor rural"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "sandbox Avalara portal"
msgstr "portal sandbox Avalara"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__securitypublicagency
msgid "securityPublicAgency"
msgstr "Secretaria nacional de segurança pública"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__service
msgid "service"
msgstr "Serviço"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplified
msgid "simplified"
msgstr "Optante do SIMPLES"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplifiedentrepreneur
msgid "simplifiedEntrepreneur"
msgstr "Empreendedor do Simples Nacional"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__simplifiedovergrossthreshold
msgid "simplifiedOverGrossthreshold"
msgstr "Optante do SIMPLES com limite de faturamento bruto"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__stockwarehouse
msgid "stockWarehouse"
msgstr "Depósito fechado"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__telco
msgid "telco"
msgstr "Serviços de telecomunicações"

#. module: l10n_br_avatax
#: model_terms:ir.ui.view,arch_db:l10n_br_avatax.res_config_settings_view_form
msgid "the Odoo documentation"
msgstr "a documentação do Odoo"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__transporter
msgid "transporter"
msgstr "Transportadora"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_tax_regime__variable
msgid "variable"
msgstr "Variável"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__waterdistributor
msgid "waterDistributor"
msgstr "Distribuidor de água"

#. module: l10n_br_avatax
#: model:ir.model.fields.selection,name:l10n_br_avatax.selection__res_partner__l10n_br_activity_sector__wholesale
msgid "wholesale"
msgstr "Atacado"
