<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="audit_report_view_form" model="ir.ui.view">
        <field name="name">audit.report.view.form</field>
        <field name="model">audit.report</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <div class="oe_title" colspan="2">
                        <div class="o_form_label">Title</div>
                        <h1><field class="text-break" options="{'line_breaks': False}" widget="text" name="title" placeholder="e.g. Annual Financial Overview"/></h1>
                    </div>
                    <div class="o_form_label">Dates</div>
                    <div class="d-flex align-items-center gap-3">
                        <field name="start_date" no_label="1"/>
                        <i class="oi oi-fw oi-arrow-right"/>
                        <field name="end_date" no_label="1"/>
                    </div>
                    <field name="responsible_user_ids" widget="many2many_avatar_user"/>
                    <field name="knowledge_template_article_id" groups="base.group_no_one" string="Audit Template"/>
                </group>
                <footer>
                    <button string="Save" class="btn-primary" special="save" data-hotkey="S"/>
                    <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="audit_report_view_kanban" model="ir.ui.view">
        <field name="name">audit.report.view.kanban</field>
        <field name="model">audit.report</field>
        <field name="arch" type="xml">
            <kanban
                create="1"
                highlight_color="color"
                js_class="audit_report_kanban_controller">
                <field name="title"/>
                <field name="status"/>
                <field name="knowledge_article_id"/>
                <templates>
                    <t t-name="menu">
                        <a type="object" name="action_set_to_draft"
                            invisible="status == 'draft'"
                            class="dropdown-item" role="menuitem">Set to Draft</a>
                        <a type="object" name="action_set_to_done"
                            invisible="status == 'done'"
                            class="dropdown-item" role="menuitem">Set to Done</a>
                        <a type="object" name="action_edit_audit_report"
                            class="dropdown-item" role="menuitem">Configure</a>
                        <a type="delete" class="dropdown-item" role="menuitem">Delete</a>
                        <hr class="dropdown-divider"/>
                        <div role="menuitem" aria-haspopup="true">
                            <field name="color" widget="kanban_color_picker"/>
                        </div>
                    </t>
                    <t t-name="card">
                        <field name="title" class="fw-bold fs-5 flex-grow-1"/>
                        <div class="py-3">
                            <div class="d-flex align-items-center">
                                <i class="fa fa-fw fa-calendar me-2" title="Calendar"/>
                                <field name="start_date"/>
                                <i class="oi oi-fw oi-arrow-right"/>
                                <field name="end_date"/>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fa fa-fw fa-building me-2" title="Company"/>
                                <field name="company_id"/>
                            </div>
                        </div>
                        <div class="d-flex align-items-end">
                            <div class="flex-grow-1">
                                <button type="object" name="action_audit_report_pdf" class="btn btn-primary">Print</button>
                            </div>
                            <div class="d-flex">
                                <div invisible="status != 'draft'" class="badge text-bg-secondary align-self-center mx-2">Draft</div>
                                <div invisible="status != 'done'" class="badge text-bg-success align-self-center mx-2">Done</div>
                                <field name="responsible_user_ids" widget="many2many_avatar_user" class="ms-auto"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="audit_report_view_search" model="ir.ui.view">
        <field name="name">audit.report.view.search</field>
        <field name="model">audit.report</field>
        <field name="arch" type="xml">
            <search>
                <field name="title"/>
                <filter name="filter_my_audit_reports" string="My Reports"
                    domain="[('create_uid', '=', uid)]"/>
                <separator/>
                <filter name="filter_draft" string="Draft"
                    domain="[('status', '=', 'draft')]"/>
                <filter name="filter_done" string="Done"
                    domain="[('status','=', 'done')]"/>
                <group>
                    <filter name="group_by_company" domain="[]" context="{'group_by': 'company_id'}"/>
                    <filter name="group_by_responsible_user_ids" domain="[]" context="{'group_by': 'responsible_user_ids'}"/>
                    <filter name="group_by_start_date" domain="[]" context="{'group_by': 'start_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_audit_report_quick_create" model="ir.actions.act_window">
        <field name="name">Create an Audit Report</field>
        <field name="res_model">audit.report</field>
        <field name="target">new</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="audit_report_view_form"/>
    </record>

    <record id="action_audit_report" model="ir.actions.act_window">
        <field name="name">Audit Reports</field>
        <field name="res_model">audit.report</field>
        <field name="view_mode">kanban</field>
        <field name="help" type="html">
            <h1>Create an Audit Report</h1>
            <div class="d-flex o_view_accountant_knowledge_nocontent_helper_container">
                <div class="flex-grow-1">
                    <img src="/accountant_knowledge/static/img/puzzle.svg"/>
                    <h2>Load a Template</h2>
                    <p>Start from our ready-to-use templates</p>
                </div>
                <i class="oi oi-arrow-right fs-2 mt-5 text-muted"/>
                <div class="flex-grow-1">
                    <img src="/accountant_knowledge/static/img/signature.svg"/>
                    <h2>Edit its content</h2>
                    <p>Customize all of its content.<br/>Add sections, pages, or attachments</p>
                </div>
                <i class="oi oi-arrow-right fs-2 mt-5 text-muted"/>
                <div class="flex-grow-1">
                    <img src="/accountant_knowledge/static/img/documents_pile.svg"/>
                    <h2>Print your Report</h2>
                    <p>Get a complete PDF file, ready to be shared</p>
                </div>
            </div>
        </field>
    </record>
</odoo>
