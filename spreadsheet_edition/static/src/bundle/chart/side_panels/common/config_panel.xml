<odoo>
    <t t-name="spreadsheet_edition.CommonOdooChartConfigPanel">
        <t t-if="!isDataLoaded">
            <div class="text-center mt-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </t>
        <t t-elif="isModelValid">
            <Section title.translate="Model">
                <div><t t-esc="modelDisplayName" /> (<t t-esc="model" />)</div>
            </Section>
            <Section title.translate="Domain">
                <DomainSelector resModel="model" domain="domain" t-key="'odoo_chart_' + props.chartId" />
                <div class="btn btn-link o_edit_domain" t-on-click="openDomainEdition">
                    <i class="oi oi-arrow-right me-1" />Edit domain
                </div>
            </Section>
            <div>
                <!-- requires <irMenuSelector> component -->
                <t t-call="spreadsheet_edition.OdooMenuChartPanelSection" />
            </div>
            <Section class="'pt-1'">
                <div class="o_pivot_last_update text-muted">
                    <i>Last updated at <t t-esc="getLastUpdate()" /></i>
                </div>
                <br />
            </Section>
        </t>
        <t t-else="1">
            <ValidationMessages messages="[invalidChartModel]" msgType="'error'" />
        </t>
    </t>
</odoo>
