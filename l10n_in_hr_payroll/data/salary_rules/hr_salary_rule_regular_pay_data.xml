<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_in_hr_payroll_structure_in_employee_salary_basic_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.BASIC"/>
        <field name="name">Basic Salary</field>
        <field name="sequence">1</field>
        <field name="code">BASIC</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = max(
    payslip._rule_parameter('l10n_in_basic_value'),
    payslip.paid_amount * payslip._rule_parameter('l10n_in_basic_percent') * payslip._rule_parameter('l10n_in_basic_days')
)
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_hra" model="hr.salary.rule">
        <field name="code">HRA</field>
        <field name="name">House Rent Allowance</field>
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="sequence" eval="16"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
hra_value = payslip._rule_parameter('l10n_in_hra_value')
result = categories['BASIC'] * hra_value
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_std" model="hr.salary.rule">
        <field name="code">STD</field>
        <field name="name">Standard Allowance</field>
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="sequence" eval="20"/>
        <field name="quantity">1</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
std_awl = payslip._rule_parameter('l10n_in_std_alw')
work_rate = 1
if payslip.worked_days_line_ids:
    total_days = sum(payslip.worked_days_line_ids.mapped('number_of_days'))
    total_unpaid_days = worked_days['LEAVE90'].number_of_days if 'LEAVE90' in worked_days else 0
    work_rate = (total_days - total_unpaid_days) / total_days
result = std_awl * work_rate
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_bonus" model="hr.salary.rule">
        <field name="code">BONUS</field>
        <field name="name">Bonus</field>
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="sequence" eval="22"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
bonus_percent = payslip._rule_parameter('l10n_in_bonus_percent')
result = 0
for treshhold, coef in bonus_percent:
    annual_wage = version.wage * coef[0]
    if annual_wage &lt; treshhold:
        result = categories['BASIC'] * coef[1]
        break
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_lta" model="hr.salary.rule">
        <field name="code">LTA</field>
        <field name="name">Leave Travel Allowance</field>
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="sequence" eval="23"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
lta_percent = payslip._rule_parameter('l10n_in_lta_percent')
result = 0
for treshhold, coef in lta_percent:
    annual_wage = version.wage * coef[0]
    if annual_wage &lt; treshhold:
        result = categories['BASIC'] * coef[1]
        break
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_spl" model="hr.salary.rule">
        <field name="code">SPL</field>
        <field name="name">Supplementary Allowance</field>
        <field name="category_id" ref="l10n_in_hr_payroll.SPA"/>
        <field name="sequence" eval="29"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
basic_and_alw = categories['BASIC'] + categories['ALW']
basic_in_per = payslip._rule_parameter('l10n_in_basic_percent')
result = payslip.paid_amount * basic_in_per - basic_and_alw
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_p_bonus" model="hr.salary.rule">
        <field name="code">P_BONUS</field>
        <field name="name">Performance Bonus</field>
        <field name="category_id" ref="l10n_in_hr_payroll.PBS"/>
        <field name="sequence" eval="31"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
basic_percent = payslip._rule_parameter('l10n_in_basic_percent')
regular_bonus_percent = payslip._rule_parameter('l10n_in_regular_bonus_percent')
worked_days_ratio = 1
if payslip.worked_days_line_ids:
    total_days = sum(payslip.worked_days_line_ids.mapped('number_of_days'))
    total_unpaid_days = worked_days['LEAVE90'].number_of_days if 'LEAVE90' in worked_days else 0
    worked_days_ratio = (total_days - total_unpaid_days) / total_days
bonus_base_ded = version.wage * basic_percent * 2 / payslip._rule_parameter('l10n_in_leave_days') + version.l10n_in_gratuity
bonus_base = version.wage * regular_bonus_percent - bonus_base_ded
result = bonus_base * worked_days_ratio
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_gross_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.GROSS"/>
        <field name="name">Taxable Salary</field>
        <field name="sequence">100</field>
        <field name="code">GROSS</field>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = payslip.paid_amount * payslip._rule_parameter('l10n_in_basic_percent')
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_leave" model="hr.salary.rule">
        <field name="code">LEAVE</field>
        <field name="name">Leave Allowance</field>
        <field name="category_id" ref="l10n_in_hr_payroll.LEAVE"/>
        <field name="sequence" eval="101"/>
        <field name="condition_select">python</field>
        <field name="condition_python">result = bool(version.l10n_in_leave_allowance)</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">result = version.l10n_in_leave_allowance</field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
        <field name="appears_on_payslip" eval="False"/>
    </record>

    <record id="l10n_in_hr_salary_rule_pt" model="hr.salary.rule">
        <field name="code">PT</field>
        <field name="name">Professional Tax</field>
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="sequence" eval="105"/>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
amounts = payslip._rule_parameter('l10n_in_professional_tax')
if categories['GROSS'] &gt;= 12000:
    result = amounts[0]
elif categories['GROSS'] &gt;= 9000 and categories['GROSS'] &lt; 12000:
    result = amounts[1]
elif categories['GROSS'] &gt;= 6000 and categories['GROSS'] &lt; 9000:
    result = amounts[2]
else:
    result = 0
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="hr_salary_rule_pf_with_pf" model="hr.salary.rule">
        <field name="code">PF</field>
        <field name="name">Provident fund - Employee</field>
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="sequence" eval="107"/>
        <field name="condition_select">python</field>
        <field name="condition_python">result = bool(version.l10n_in_provident_fund)</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
total_days = sum(payslip.worked_days_line_ids.mapped('number_of_days'))
total_worked_days = total_days - ('LEAVE90' in worked_days and worked_days['LEAVE90'].number_of_days)
result = - categories['BASIC'] * payslip._rule_parameter('l10n_in_pf_percent') if  ((categories['BASIC']) &lt; 15000) else -(1800/total_days) * total_worked_days
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="hr_salary_rule_pfe_with_pf" model="hr.salary.rule">
        <field name="code">PFE</field>
        <field name="name">Provident fund - Employer</field>
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="sequence" eval="110"/>
        <field name="condition_select">python</field>
        <field name="condition_python">result = bool(version.l10n_in_provident_fund)</field>
        <field name="amount_select">code</field>
         <field name="amount_python_compute">
total_days = sum(payslip.worked_days_line_ids.mapped('number_of_days'))
total_worked_days = total_days - ('LEAVE90' in worked_days and worked_days['LEAVE90'].number_of_days)
result = - categories['BASIC'] * payslip._rule_parameter('l10n_in_pf_percent') if  ((categories['BASIC']) &lt; 15000) else -(1800/total_days) * total_worked_days
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payslip_rule_tds" model="hr.salary.rule">
        <field name="code">TDS</field>
        <field name="name">Tax Deducted at Source</field>
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="condition_select">python</field>
        <field name="condition_python">result = bool(version.l10n_in_tds)</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">result = -(version.l10n_in_tds)</field>
        <field name="sequence" eval="140"/>
        <field name="note">As per income tax rules, all payment which are taxable in nature should be done after deduction of taxes at the source itself. Hence employer compute income tax on salary payment and deduct it every month. This TDS is based on employee’s saving/investment declaration at the start of year. If investments for tax saving is not done, large amount may be deducted in last few months.</field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_attachment_of_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Attachment of Salary</field>
        <field name="sequence">174</field>
        <field name="code">ATTACH_SALARY</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'ATTACH_SALARY' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['ATTACH_SALARY'].amount
result_name = inputs['ATTACH_SALARY'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_assignment_of_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Assignment of Salary</field>
        <field name="sequence">174</field>
        <field name="code">ASSIG_SALARY</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'ASSIG_SALARY' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['ASSIG_SALARY'].amount
result_name = inputs['ASSIG_SALARY'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_child_support" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Child Support</field>
        <field name="code">CHILD_SUPPORT</field>
        <field name="amount_select">code</field>
        <field name="sequence">174</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'CHILD_SUPPORT' in inputs</field>
        <field name="amount_python_compute">
result = -inputs['CHILD_SUPPORT'].amount
result_name = inputs['CHILD_SUPPORT'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_salary_rule_expenses_reimbursement" model="hr.salary.rule">
        <field name="name">Expenses Reimbursement</field>
        <field name="code">EXPENSES</field>
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="sequence" eval="190"/>
        <field name="condition_select">python</field>
        <field name="condition_python">result = inputs['EXPENSES'].amount > 0.0 if 'EXPENSES' in inputs else False</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = inputs['EXPENSES'].amount
result_name = inputs['EXPENSES'].name
        </field>
        <field name="struct_id" ref="l10n_in_hr_payroll.hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_deduction_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.DED"/>
        <field name="name">Deduction</field>
        <field name="sequence">198</field>
        <field name="code">DEDUCTION</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'DEDUCTION' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = -inputs['DEDUCTION'].amount
result_name = inputs['DEDUCTION'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_reimbursement_salary_rule" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.ALW"/>
        <field name="name">Reimbursement</field>
        <field name="sequence">199</field>
        <field name="code">REIMBURSEMENT</field>
        <field name="condition_select">python</field>
        <field name="condition_python">result = 'REIMBURSEMENT' in inputs</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = inputs['REIMBURSEMENT'].amount
result_name = inputs['REIMBURSEMENT'].name
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>

    <record id="l10n_in_hr_payroll_structure_in_employee_salary_net_salary" model="hr.salary.rule">
        <field name="category_id" ref="hr_payroll.NET"/>
        <field name="name">Net Salary</field>
        <field name="sequence">200</field>
        <field name="code">NET</field>
        <field name="appears_on_employee_cost_dashboard" eval="True"/>
        <field name="condition_select">none</field>
        <field name="amount_select">code</field>
        <field name="amount_python_compute">
result = categories['BASIC'] + categories['ALW'] + categories['DED']
        </field>
        <field name="struct_id" ref="hr_payroll_structure_in_employee_salary"/>
    </record>
</odoo>
