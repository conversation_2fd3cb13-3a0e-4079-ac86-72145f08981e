<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr.structure_type_worker" model="hr.payroll.structure.type">
        <field name="wage_type">hourly</field>
    </record>

    <record id="hr_payroll_salary_structure_type_ind_emp_pay" model="hr.payroll.structure.type">
        <field name="name">India: Employee Pay</field>
        <field name="country_id" ref="base.in"/>
    </record>
    <record id="hr_payroll_salary_structure_type_ind_intern" model="hr.payroll.structure.type">
        <field name="name">India: Intern</field>
        <field name="country_id" ref="base.in"/>
    </record>
    <record id="hr_payroll_salary_structure_type_ind_worker" model="hr.payroll.structure.type">
        <field name="name">India: Worker</field>
        <field name="country_id" ref="base.in"/>
    </record>
    <record id="hr_payroll_salary_structure_type_ind_emp" model="hr.payroll.structure.type">
        <field name="name">India: Non-Executives</field>
        <field name="country_id" ref="base.in"/>
    </record>
</odoo>
