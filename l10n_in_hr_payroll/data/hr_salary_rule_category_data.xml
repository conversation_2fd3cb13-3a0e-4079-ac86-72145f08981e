<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- salary category -->
    <record id="SPA" model="hr.salary.rule.category">
        <field name="name">Special Allowance</field>
        <field name="code">SPA</field>
        <field name="country_id" ref="base.in"/>
    </record>

    <record id="LEAVE" model="hr.salary.rule.category">
        <field name="name">Leave Allowance</field>
        <field name="code">LEAVE</field>
        <field name="country_id" ref="base.in"/>
    </record>

    <record id="PBS" model="hr.salary.rule.category">
        <field name="name">Performance Bonus</field>
        <field name="code">PBS</field>
        <field name="parent_id" ref="hr_payroll.DED"/>
        <field name="country_id" ref="base.in"/>
    </record>

</odoo>
