<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_template_view_form" model="ir.ui.view">
        <field name="name">hr.contract.template.view.form.inherit.l10_sk_hr_payroll</field>
        <field name="model">hr.version</field>
        <field name="inherit_id" ref="hr.hr_contract_template_form_view"/>
        <field name="arch" type="xml">
            <group name="salary_left" position="inside">
                <label for="l10n_sk_meal_voucher_employee" invisible="country_code != 'SK'"/>
                <div class="o_row" invisible="country_code != 'SK'">
                    <field name="l10n_sk_meal_voucher_employee" widget="monetary" options="{'currency_field': 'currency_id'}" class="o_hr_narrow_field"/>
                    <span>/ voucher</span>
                </div>
                <label for="l10n_sk_meal_voucher_employer" invisible="country_code != 'SK'"/>
                <div class="o_row" invisible="country_code != 'SK'">
                    <field name="l10n_sk_meal_voucher_employer" widget="monetary" options="{'currency_field': 'currency_id'}" class="o_hr_narrow_field"/>
                    <span>/ voucher</span>
                </div>
            </group>
        </field>
    </record>
</odoo>
