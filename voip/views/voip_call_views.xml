<?xml version="1.0"?>
<odoo>
    <record id="voip_call_tree_view" model="ir.ui.view">
        <field name="name">VoIP Calls list view</field>
        <field name="model">voip.call</field>
        <field name="arch" type="xml">
            <list string="Calls" create="false">
                <field name="create_date" string="Date" widget="voip_simple_datetime"/>
                <field name="partner_id" string="Contact"/>
                <field name="company_id" optional="hide"/>
                <field name="phone_number" widget="voip_flag_phone"/>
                <field name="country_flag_url" column_invisible="True"/> <!-- Needed for voip_flag_phone widget -->
                <field name="duration" widget="float_time" options="{'displaySeconds': True}" invisible="not (start_date and end_date)"/>
                <field name="user_id" string="User" widget="many2one_avatar_user"/>
                <field name="country_id" optional="hide"/>
                <field name="provider_id" optional="hide"/>
                <field name="state" string="Status" widget="voip_call_status_badge"/>
                <field name="direction" column_invisible="True"/>
                <field name="id" column_invisible="True"/>
            </list>
        </field>
    </record>

    <record id="voip_call_action_history" model="ir.actions.act_window">
        <field name="name">History</field>
        <field name="res_model">voip.call</field>
        <field name="view_mode">list,pivot,graph,form</field>
        <field name="context">{"search_default_my_calls": True}</field>
    </record>

    <record id="voip_call_view_pivot" model="ir.ui.view">
        <field name="name">voip.call.view.pivot</field>
        <field name="model">voip.call</field>
        <field name="arch" type="xml">
            <pivot string="Calls Analysis">
                <field name="user_id" type="row"/>
                <field name="create_date" type="col"/>
            </pivot>
        </field>
    </record>

    <record id="voip_call_view_graph" model="ir.ui.view">
        <field name="name">voip.call.view.graph</field>
        <field name="model">voip.call</field>
        <field name="arch" type="xml">
            <graph string="Calls Analysis">
                <field name="user_id"/>
            </graph>
        </field>
    </record>

    <record id="voip_call_view_search" model="ir.ui.view">
        <field name="name">voip.call.view.search</field>
        <field name="model">voip.call</field>
        <field name="arch" type="xml">
            <search>
                <field name="partner_id"/>
                <field name="phone_number"/>
                <field name="user_id"/>
                <filter string="My Calls" name="my_calls" domain="[('user_id', '=', uid)]"/>
                <separator/>
                <filter string="Internal" name="internal" domain="[('is_within_same_company', '=', True)]"/>
                <filter string="External" name="external" domain="[('is_within_same_company', '=', False)]"/>
                <separator/>
                <filter string="Incoming" name="incoming" domain="[('direction', '=', 'incoming')]"/>
                <filter string="Outgoing" name="outgoing" domain="[('direction', '=', 'outgoing')]"/>
                <filter string="Missed" name="missed" domain="[('state', '=', 'missed')]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('create_date', '>=', context_today())]"/>
                <filter string="Yesterday" name="yesterday" domain="[('create_date', '&lt;', context_today()), ('create_date', '>=', context_today() - relativedelta(days=1))]"/>
                <filter string="Last 7 Days" name="last_7_days" domain="[('create_date','>=', context_today() - relativedelta(days=6))]"/>
                <group name="group_by">
                    <filter string="User" name="groupby_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Contact" name="groupby_contact" context="{'group_by': 'partner_id'}"/>
                    <filter string="Status" name="groupby_status" context="{'group_by': 'state'}"/>
                    <separator/>
                    <filter string="Date" name="groupby_date" context="{'group_by': 'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="voip_call_view_form" model="ir.ui.view">
        <field name="name">voip.call.form</field>
        <field name="model">voip.call</field>
        <field name="arch" type="xml">
            <form create="false">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_calls" type="object" class="oe_stat_button" icon="fa-phone">
                            <field name="call_count" widget="statinfo" string="Calls"/>
                        </button>
                    </div>
                    <div class="mb8">
                        <div class="d-flex gap-3 justify-content-between align-items-start">
                            <div class="d-flex gap-3">
                                <field name="image_1920" widget="contact_image" options="{'preview_image': 'avatar_128', 'size': [130,130], 'img_class': 'rounded border'}" invisible="not partner_id"/>
                                <field name="avatar_128" invisible="True"/> <!-- Needed in contact_image widget -->
                                <img src="/base/static/img/avatar_grey.png" class="rounded border" width="130" height="130" invisible="partner_id" alt="Contact Avatar"/>
                                <div class="d-flex flex-column flex-grow-1">
                                    <h1 class="mb-0">
                                        <field name="partner_id" nolabel="True" placeholder="e.g. Brandon Freeman"/>
                                    </h1>
                                    <div class="d-flex align-items-baseline">
                                        <i class="fa fa-fw me-1 fa-phone text-primary" title="Phone"/>
                                        <field name="country_flag_url" widget="image_url" options='{"size": [20, 20]}' nolabel="True" class="me-1"/>
                                        <field name="phone_number" widget="phone"/>
                                    </div>
                                </div>
                            </div>
                            <field name="state" widget="voip_call_status_badge" nolabel="True" class="float-end"/>
                            <!-- Needed for voip_call_status_badge widget -->
                            <field name="direction" invisible="True"/>
                            <field name="id" invisible="True"/>
                        </div>
                    </div>
                    <group>
                        <group>
                            <field name="create_date" widget="voip_simple_datetime"/>
                            <field name="duration" widget="float_time" options="{'displaySeconds': True}" readonly="True" invisible="not (start_date and end_date)"/>
                        </group>
                        <group>
                            <field name="user_id" string="User" widget="many2one_avatar_user" readonly="True"/>
                            <field name="provider_id" readonly="True"/>
                            <field name="company_id" readonly="True"/>
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="voip_call_view" model="ir.actions.act_window">
        <field name="name">Calls</field>
        <field name="res_model">voip.call</field>
        <field name="view_mode">list,graph,pivot,form</field>
        <field name="view_id" ref="voip_call_tree_view"/>
        <field name="domain">[]</field>
    </record>
</odoo>
