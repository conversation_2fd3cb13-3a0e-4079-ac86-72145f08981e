<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="hr_attendance_gantt_create_view_form" model="ir.ui.view">
        <field name="name">hr.attendance.form</field>
        <field name="model">hr.attendance</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='check_out']" position="attributes">
                <attribute name="invisible">check_out == False</attribute>
            </xpath>
            <xpath expr="//separator" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
            <xpath expr="//separator[@name='check_out_separator']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
            <xpath expr="//group[@name='check_in_group']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
            <xpath expr="//group[@name='check_out_group']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
            <form position="inside">
                <footer>
                    <button
                        string="Save &amp; Close"
                        special="save"
                        data-hotkey="s"
                        class="btn btn-primary"
                        close="1"
                        invisible="not is_manager"/>
                    <button
                        string="Discard"
                        special="cancel"
                        data-hotkey="j"
                        class="btn-secondary"
                        close="1"
                        invisible="not is_manager"/>
                    <button
                        string="Delete"
                        name="unlink"
                        type="object"
                        data-hotkey="x"
                        class="btn btn-danger ms-auto"
                        close="1"
                        invisible="not id or not is_manager"/>
                    <button
                        string="Close"
                        special="cancel"
                        data-hotkey="j"
                        class="btn-secondary"
                        close="1"
                        invisible="is_manager"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Gantt view -->
    <record id="hr_attendance_gantt_view" model="ir.ui.view">
        <field name="name">hr.attendance.gantt</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <gantt
                js_class="attendance_gantt"
                date_start="check_in"
                date_stop="check_out"
                string="Days"
                color="color"
                default_group_by="employee_id"
                default_scale="day"
                default_range="day"
                precision="{'day': 'hour:quarter', 'week': 'day:quarter', 'week_2': 'day:half', 'month': 'day:half'}"
                plan="0"
                progress="overtime_progress"
                progress_bar="employee_id"
                display_unavailability="1"
                form_view_id="%(hr_attendance_gantt.hr_attendance_gantt_create_view_form)d"
            >
            </gantt>
        </field>
    </record>

    <record id="hr_attendance.hr_attendance_action" model="ir.actions.act_window">
        <field name="view_mode">gantt,list,form</field>
    </record>
</odoo>
